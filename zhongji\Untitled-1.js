// 自动填写简答题答案
(function() {
    // 简答题答案库
    const shortAnswerQuestions = [
        {
            question: '当今科技发展浪潮中，人工智能技术在不同行业和生活场景里发挥着重要作用，请列举五个典型的应用领域，并分别给出具体的应用实例。相较于传统技术，人工智能在实际应用中展现出独特的优势与特性。请概括人工智能应用所具备的五个显著特点，并对每个特点进行简要说明。',
            answer: '人工智能的应用：1.自然语言处理：语音助手、翻译、聊天机器人。2.计算机视觉：图像识别、自动驾驶、医疗影像分析。3.机器学习：推荐系统、预测分析、欺诈检测。4.自动驾驶与机器人：无人车、无人机、自动化机器人。5.医疗：疾病诊断、个性化医疗、健康监测。6.金融：智能投资、风险管理、自动交易。7.娱乐：游戏AI、内容生成、虚拟现实。8.智能家居：智能设备控制、家居自动化。人工智能的应用特点:1.自学习能力：通过数据训练，不断优化表现。2.自动化：减少人工干预，自动执行复杂任务。3.处理海量数据：高效分析和处理大规模数据集。4.模式识别：识别图像、声音、文本等复杂模式。5.智能决策：根据数据和环境做出最佳选择。6.适应性：根据经验和新信息进行调整和改进。'
        },
        {
            question: '当图像数据出现部分遮挡、画面模糊的情况时，怎样合理开展标注工作以确保标注结果的准确性？',
            answer: '对于遮挡问题，若遮挡部分不影响主要目标识别，可根据可见部分和上下文信息进行标注；若影响较大，可标记为遮挡状态或通过多视角图像辅助标注。对于模糊问题，尽量利用图像中的清晰部分和先验知识进行标注，若无法准确标注，可标记为模糊待处理，后续根据更多信息或专业人员判断。'
        },
        {
            question: '为了让机器更精准地 "听懂" 人类语言，在语音识别技术的实际应用与优化过程中，可通过哪些途径来提升其识别的精确程度？',
            answer: '可以采取以下方法：增加训练数据量，以涵盖更多的语音变化；使用更先进的声学模型和语言模型；进行数据增强，如添加噪声、语速变化等；优化模型的超参数；使用多模态信息（如结合唇形等视觉信息）；对语音数据进行预处理，如去除噪声等。'
        },
        {
            question: '在数据预处理环节，将数据转化为零均值、单位方差分布的标准化操作，与把数据映射至固定区间（如 [0,1]）的归一化处理，二者存在哪些本质差异？',
            answer: '数据标准化是将数据转换为具有零均值和单位方差的分布，使得不同特征在数值上具有可比性；而归一化是将数据映射到特定的范围（如 [0,1]）内。标准化更关注数据的分布特性，而归一化主要是为了消除不同特征量纲的影响，方便后续的计算和处理。'
        },
        {
            question: '从计算机视觉和人工智能角度出发，能够实现车辆关键信息自动提取，并广泛应用于停车管理等场景的技术，如何定义？其涵盖哪些常见功能？当利用技术手段对车辆进行识别时，从原始图像到形成有效识别数据，需要经过怎样的处理过程？请按步骤进行说明',
            answer: '车辆识别是一种基于计算机视觉和人工智能的技术，用于自动检测和识别图像或视频中的车辆类型、车牌号码、品牌型号等信息。它通常应用在智能交通系统、停车管理、安防监控等领域。车辆识别的常见功能包括：1.车牌识别（LPR/ANPR）：自动识别和读取车牌号。2.车辆分类：区分车辆类型，如轿车、卡车、公交车等。3.车辆品牌和型号识别：识别车辆的品牌、型号、颜色等外观特征。处理过程：1.图像采集 2.图像预处理 3.车辆检测 4.特征提取 5.分类与识别 6.结果输出 7.数据存储与处理'
        },
        {
            question: '语料标准化是将客户的原始语料相似问题转化成标准问题的一个过程，为了使在线机器人更高效、准确的识别问题，需要将已经清洗处理的客户原始语料转化为标准语料。假如你是某电商平台智能客服系统的语料整理员，需要在以下客户原始语料中找出与标准语料"如何申请退款？"对应的语料（退款指将已支付款项退回原支付账户）',
            answer: '1、3、4、5、6、8、9'
        },
        {
            question: '命名实体识别在自然语言处理中的主要目标是什么？它对其他NLP任务有何重要性？',
            answer: '任务是识别文本中的具有特定意义的实体，如人名、地名、组织机构名等。意义在于为进一步的文本理解和处理提供基础，有助于信息抽取、问答系统、机器翻译等任务的实现。例如，在信息抽取中可以快速提取关键实体信息，提高处理效率。'
        },
        {
            question: '在将训练好的模型投入实际应用时，需要评估哪些技术要素以确保系统高效稳定运行？',
            answer: '要考虑目标平台的性能和资源限制，如计算能力、内存等；模型的压缩和优化，以减少模型大小和计算量；接口设计，使其能与其他系统进行交互；实时性要求，确保模型能够及时响应；安全性和稳定性，保证模型的可靠运行；还需考虑部署的成本和维护的便捷性等。'
        },
        {
            question: '在人工智能技术范畴内，有一种技术能够让计算机像人类一样 "认识" 图像，它借助计算机视觉算法，对图像中的物体、场景等进行自动分辨归类，还能运用机器学习和深度学习模型，从图像和视频里提取并分析各类信息，在身份验证、交通管理、文档处理等方面发挥着重要作用。请问这项技术是什么？请阐述其定义。在深度学习兴起之前，科研人员依靠一系列既定流程实现图像识别任务，这些流程环环相扣，共同完成对图像的识别与分类工作。请系统地梳理传统图像识别的具体流程',
            answer: '图像识别是人工智能领域中的一项技术，指的是通过计算机视觉算法，自动识别和分类图像中的物体、场景或特定特征。它利用机器学习和深度学习模型，能够从图片或视频中提取信息并进行分析，常见的应用包括：面部识别：识别和验证人的面部特征。物体检测：识别图像中的不同物体，如车辆、动物等。文字识别（OCR）：从图像中提取和识别文字。传统图像识别流程：1.图像预处理 2.特征提取 3.特征选择 4.分类与匹配 5.结果输出'
        },
        {
            question: '人工智能的定义及分类。人工智能与计算机程序相比对，优点有那些？',
            answer: '人工智能（AI） 是一种使机器具备模拟或执行人类智能行为的技术，包含感知、推理、学习、规划和决策等能力。AI可以通过数据分析、模式识别和自我改进来完成复杂任务，通常分为三类：弱人工智能、强人工智能和超人工智能。人工智能与计算机程序相比对，优点有：（1）具备自主学习和适应能力：1.传统计算机程序：根据预定规则和算法执行特定任务。其行为是确定性的，依赖于编写者的指令。2.人工智能：具备自主学习和适应能力，能够从数据中学习，并根据环境或经验调整行为。（2）灵活性更高：1.计算机程序：严格遵循编写时定义的规则，无法自主改进或扩展。2.人工智能：具备自我学习和调整的能力，通过训练算法提高表现，例如机器学习中的模型可以通过数据进行训练并优化。（3）处理复杂性能力更强：1.传统程序：适合处理简单、明确的任务，无法应对不确定性和模糊的情况。2.人工智能：擅长处理复杂的、模糊或不确定的情况，可以进行模式识别和预测。'
        },
        {
            question: '有一种模型通过让两个组件展开 "猫鼠游戏" 来提升数据生成质量，解释该模型的工作原理以及这两个组件的具体职责。',
            answer: '基本原理是通过生成器和判别器的对抗训练来生成逼真的数据。生成器试图生成假数据来欺骗判别器，而判别器则要区分真实数据和生成器生成的数据。主要组成部分为生成器和判别器。生成器学习生成逼真的数据，判别器则不断提升区分能力，两者相互竞争，促使生成器生成更接近真实数据的样本。'
        }
    ];

    // 获取所有简答题文本框
    const textareas = document.querySelectorAll('textarea[name^="TLX"]');
    
    // 遍历所有文本框
    textareas.forEach(textarea => {
        // 获取题目文本
        const questionDiv = textarea.closest('div[name^="DIV"]');
        if (!questionDiv) return;
        
        const questionTd = questionDiv.querySelector('td[valign="top"][style*="width"]');
        if (!questionTd) return;
        
        const paragraphs = questionTd.querySelectorAll('p');
        if (paragraphs.length < 1) return;
        
        const questionText = paragraphs[0].textContent.trim();
        // 移除题号和括号
        const cleanQuestion = questionText.replace(/^\d+、\s*|（\s*）\s*。*$/g, '');
        
        // 在答案库中查找匹配的题目
        for (const qa of shortAnswerQuestions) {
            // 使用相似度匹配，如果题目包含关键词就认为匹配
            if (cleanQuestion.includes(qa.question.substring(0, 15)) || 
                qa.question.includes(cleanQuestion.substring(0, 15))) {
                
                // 填写答案
                textarea.value = qa.answer;
                console.log(`已填写题目: ${cleanQuestion.substring(0, 20)}...`);
                
                // 获取保存按钮并点击
                const saveButton = textarea.parentElement.querySelector('input[type="button"][value="保存"]');
                if (saveButton) {
                    setTimeout(() => {
                        saveButton.click();
                        console.log(`已保存答案: ${cleanQuestion.substring(0, 20)}...`);
                    }, 500); // 延迟500ms再点击保存
                }
                
                break;
            }
        }
    });
    
    console.log('简答题自动填写完成！');
})();