
<!DOCTYPE>
<html>
<head id="Head1"><title>
	新职汇 
</title>
    <!--禁用缓存部分开始-->
    <meta http-equiv="Expires" content="0" /><meta http-equiv="Progma" content="no-cache" /><meta http-equiv="cache-control" content="no-cache,must-revalidate" />
    <!--禁用缓存部分结束-->
    <link rel="Stylesheet" type="text/css" href="style/paperAllCss.css" />

    <script type="text/javascript" src="Js/jquery.js"></script>

    <script src="Js/jquery-1.9.0.js" type="text/javascript"></script>

    <script src="Js/VideoPlayExam.js" type="text/javascript"></script>

    <script src="Js/uploadjs/Q.js" type="text/javascript"></script>

    <script src="Js/uploadjs/Q.Uploader.js" type="text/javascript"></script>

    <script src="Js/uploadjs/Q.Uploader.Image.js" type="text/javascript"></script>

    <script type="text/javascript">
        window.history.forward(1);
        var tid;
        function $(_sId) {
            return document.getElementById(_sId);
        }
        //传参跳转psaveN
        function gotoPsaveN(isJudge) {
            var strUrl = "psaveN.aspx?stuno=" + document.getElementById("frmStuNo").value +
                               "&exmClsId=" + encodeURI(document.getElementById("frmExmClsID").value) +
                               "&exmNo=" + encodeURI(document.getElementById("frmUpExmNo").value) +
                                "&exmType=" + encodeURI(document.getElementById("frmExmType").value) +
                                "&clsName=" + encodeURI(document.getElementById("frmClsName").value) +
                                "&exmName=" + encodeURI(document.getElementById("frmExmName").value) +
                                "&isJudge=" + isJudge;
            if (document.getElementById("frmcur_id").value != "") {
                strUrl += "&cur_id=" + encodeURI(document.getElementById("frmcur_id").value);
            }

            //             "&exmPaperLock=" + encodeURI(document.getElementById("frmPaperLock").value) +
            //             "&exmType=" + encodeURI(document.getElementById("frmExmType").value) +
            //            exmNo=" + encodeURI(document.getElementById("frmUpExmNo").value) +
            document.location.href = strUrl;

        }

        function gotoStuMain() {
            var strUrl = "NewStudent/stuindex.aspx";
            document.location.href = strUrl;
        }
        function gotoApplyverify() {
            var strUrl = "web/trainproject/applyverify.aspx?type=0&cls_Id=0";
            if (document.getElementById("frmcur_id").value != "") {
                strUrl = "WEB/CurriCulum/StuCurExam.aspx?cur_id=" + encodeURI(document.getElementById("frmcur_id").value);
            }
            document.location.href = strUrl;
        }
        function setDilogValue() {
            document.getElementById("oElDilog").value = "";
        }
        var intervalId = window.setInterval("SecondTicker()", 1000);
        function lockPaperCheck() {
            if (document.getElementById("frmPaperLock").value == "True") {
                var mmc = 0;
                var totaln = document.getElementById("lockNum").value;
                var outPaper = false;
                oElement = document.elementFromPoint(event.x, event.y);
                //                alert(oElement.tagName); 
                if (oElement.tagName == "HTML") {
                    if (document.getElementById("oElDilog").value != "") {
                        document.getElementById("openWindowS").value = "1";
                    }
                    else {
                        if (document.getElementById("oEl").value == "SELECT" || document.getElementById("oEl").value == "INPUT") {
                            document.getElementById("openWindowS").value = "1";
                        }
                        else if (document.getElementById("oEl").value == "DIV" && document.getElementById("oElId").value == "uppaper") {
                            document.getElementById("openWindowS").value = "1";
                        }
                        else {
                            document.getElementById("openWindowS").value = "0";
                        }
                    }
                }
                //                alert(oElement.id);
                document.getElementById("oEl").value = oElement.tagName;
                document.getElementById("oElId").value = oElement.id;
                //                if(oElement.id == "D1")
                //                {
                //                    alert("ddf");
                //                    document.getElementById("openWindowS").value ="1"
                //                }
                //                else
                //                { 
                //                    document.getElementById("openWindowS").value ="0"
                //                }

                mmc = parseInt(document.getElementById("jishu").value)
                var windowobj = jQuery(window);
                var yy = windowobj.height();
                var xx = windowobj.width();
                //alert(event.y);
                //alert(event.x);

                if (event.x <= 1 || event.y <= 1) {
                    outPaper = true;
                }
                if (event.x >= xx || event.y >= yy) {
                    outPaper = true;
                }
                if (outPaper && document.getElementById("openWindowS").value == "0") {
                    mmc++;
                    document.getElementById("jishu").value = mmc;
                    if (mmc > totaln) {
                        JudgeUploadPaper();
                    }
                    else {
                        //                        alert(oElement.tagName); alert(oElement.id); 
                        alert("您已经离开试卷网页" + mmc + "次，离开超过" + totaln + "次将自动交卷！");
                    }
                }
            }
        }

        var mediaTrack = null;
        function Window_OnLoad() {
            //initAd();
            var quesioncount = document.getElementById("quesioncount");
            if (quesioncount == null) {//试卷生成错误时，此元素不存在时，预防报错
                return false;
            }

            var quesionnum = quesioncount.value;
            var loadgroupnum = document.getElementById("loadgroupnum").value;
            var loadgroupname = document.getElementById("loadgroupname").value;

            questionload(quesionnum, loadgroupnum, loadgroupname);

            hiddenSC();
            var testDir = document.getElementById("frmTestDir").value;
            var testSysDir = document.getElementById("frmTestSysDir").value;
            var testSysBakDir = document.getElementById("frmTestSysBakDir").value;
            var type = document.getElementById("frmType").value;

            var exmTimeMode = document.getElementById("frmTimeMode").value;
            document.getElementById("frmLeftMinutes").value = document.getElementById("frmMinutes").value;
            $("dispRemainM").value = Number($("frmLeftMinutes").value) - 1;

            if (parseInt(document.getElementById("frmMinutes").value) == 0) {
                //document.getElementById("spanTimeType").innerHTML = "已考时间：";
                $("dispRemainM").value = Number($("frmLeftMinutes").value);
            }

            //var bbstr = document.getElementById("D1").value + "pt";
            //jQuery("#papertable").find("td[valign='top']").each(function() {
            //jQuery(this).css({ "font-size": bbstr, "line-height": bbstr });
            //});

            document.getElementById("D1").value = "13";
            var bbstr = document.getElementById("D1").value + "pt";
            jQuery("#papertable").find("td[valign='top']").each(function() {
                jQuery(this).css({ "font-size": bbstr, "line-height": bbstr });
            });


            jQuery("[idt='dvs']").hide();
            jQuery("[ids='tr_1']").show();


            var lcIniStr = 0;
            var lnExmType = 0;
            if (!isNaN(lcIniStr) == true) {
                lnExmType = parseInt(lcIniStr);
            }

            if (lnExmType == -1 && parseInt(bs.GetStuLeftMinutes()) < 0) {
                showMsg("考试时间到，请按顺序退场！");
                document.getElementById("txtCommand").value = "PaperNUnlock//";
                document.getElementById("btnCallVB").click();
                window.setTimeout("JudgeUploadPaper()", 3000);
            }
            if (parseInt(document.getElementById("frmLeftMinutes").value) < 0) {
                showMsg("考试时间到，请按顺序退场！");
                document.getElementById("txtCommand").value = "PaperNUnlock//";
                document.getElementById("btnCallVB").click();
                window.setTimeout("JudgeUploadPaper()", 3000);
            }
            if (parseInt(document.getElementById("frmLeftMinutes").value) == 0) {
                if (parseInt(document.getElementById("frmMinutes").value) != 0) {
                    document.getElementById("bottom").style.display = "none";
                    //document.getElementById("dTitle").style.display = "none";
                    document.getElementById("description").style.display = "none";
                    alert("请首先生成试卷，然后才能进入练习或考试");
                    document.getElementById("txtCommand").value = "PaperNUnlock//";
                    document.getElementById("btnCallVB").click();
                    if (document.getElementById("frmPaperLock").value == "True") {
                        ExitTest();
                    }
                    else {

                        gotoStuMain();

                    }
                }
            }
            tid = window.setInterval("ticker()", 60000);

            showCamera();
        }
        function ticker() {
            if (checkabandoned()) {
                showMsg("您本次考试已被管理员作废！");
                if (document.getElementById("frmPaperLock").value == "True") {
                    ExitTest();
                }
                else {
                    window.setTimeout("gotoStuMain();", 3000);
                }
            }
            else {
                var totalExmMinutes = Number($("frmMinutes").value)
                if (totalExmMinutes == 0) {
                    var hasMinutes = Number($("frmLeftMinutes").value) + 1;
                    document.getElementById("frmLeftMinutes").value = hasMinutes;
                    $("dispRemainM").value = Number($("frmLeftMinutes").value);
                    return;
                }

                var lnMinutes = Number($("frmLeftMinutes").value) - 1;
                document.getElementById("frmLeftMinutes").value = lnMinutes;
                var Exmclsid = document.getElementById("frmExmClsID").value;
                var lcfrmstuno = document.getElementById("frmStuNo").value;
                //if (Number(document.getElementById("frmLeftMinutes").value) % 2 == 1) {
                var isunlockmin = document.getElementById("hidIsUnlockMin").value;
               
                if (isunlockmin == "0") {
                    if (Number(document.getElementById("frmLeftMinutes").value) == 20 || Number(document.getElementById("frmLeftMinutes").value) == 10
                         || Number(document.getElementById("frmLeftMinutes").value) == 5 || Number(document.getElementById("frmLeftMinutes").value) == 4
                         || Number(document.getElementById("frmLeftMinutes").value) == 3 || Number(document.getElementById("frmLeftMinutes").value) == 2
                         || Number(document.getElementById("frmLeftMinutes").value) == 1) {
                        var lcRetStr = loadXMLDoc(Exmclsid, lcfrmstuno);
                        if (typeof (lcRetStr) != "undefined") {
                            if (lcRetStr == "qzjj")//强制交卷
                            {
                                window.clearInterval(tid);
                                window.clearInterval(intervalId);
                                showMsg("你已被强制交卷，请按顺序退场！");
                                window.setTimeout("JudgeUploadPaper()", 4000);
                                document.getElementById("txtCommand").value = "PaperNUnlock//";
                                document.getElementById("btnCallVB").click();
                            }
                            else if (lcRetStr == "norecord") {
                                window.clearInterval(tid);
                                window.clearInterval(intervalId);
                                alert("你已被重考处理，如有异议请联系管理员！");
                                window.history.back();
                            }
                            else {
                                if (isNaN(lcRetStr) == false) {
                                    lnMinutes = parseInt(lcRetStr);
                                    document.getElementById("frmLeftMinutes").value = lnMinutes;
                                    $("dispRemainM").value = lnMinutes;
                                }
                                else {
                                    document.getElementById("openWindowS").value = "1";
                                    alert("请立即刷新网页校正时间！");
                                    window.location.reload();
                                    return;
                                }
                            }
                        }
                    }
                }
                else {
                    if (Number(document.getElementById("frmLeftMinutes").value) % Number(isunlockmin) == 1) {
                        var lcRetStr = loadXMLDoc(Exmclsid, lcfrmstuno);
                        
                        if (typeof (lcRetStr) != "undefined") {
                            if (lcRetStr == "qzjj")//强制交卷
                            {
                                window.clearInterval(tid);
                                window.clearInterval(intervalId);
                                showMsg("你已被强制交卷，请按顺序退场！");
                                window.setTimeout("JudgeUploadPaper()", 4000);
                                document.getElementById("txtCommand").value = "PaperNUnlock//";
                                document.getElementById("btnCallVB").click();
                            }
                            else if (lcRetStr == "norecord") {
                                window.clearInterval(tid);
                                window.clearInterval(intervalId);
                                alert("你已被重考处理，如有异议请联系管理员！");
                                window.history.back();
                            }
                            else {
                                if (isNaN(lcRetStr) == false) {
                                    lnMinutes = parseInt(lcRetStr);
                                    document.getElementById("frmLeftMinutes").value = lnMinutes;
                                    $("dispRemainM").value = lnMinutes;
                                }
                                else {
                                    document.getElementById("openWindowS").value = "1";
                                    alert("请立即刷新网页校正时间！");
                                    window.location.reload();
                                    return;
                                }
                            }
                        }
                    }
                }
                if (lnMinutes <= 3 && lnMinutes > 0) {
                    var tt = document.getElementById("frmLeftMinutes").value;
                    showMsg("剩余" + tt + "分钟，请注意存盘！ ");
                }
                if (lnMinutes <= 0) {
                    window.clearInterval(tid);
                    window.clearInterval(intervalId);
                    showMsg("考试时间到，请按顺序退场！");
                    window.setTimeout("JudgeUploadPaper()", 3000);
                    document.getElementById("txtCommand").value = "PaperNUnlock//";
                    document.getElementById("btnCallVB").click();
                }
                if (lnMinutes <= -3) {
                    window.clearInterval(tid);
                    window.clearInterval(intervalId);
                    //传参跳转psaveN
                    showMsg("考试时间已过，不允许考试！");
                    window.setTimeout("JudgeUploadPaper()", 3000);
                    document.getElementById("txtCommand").value = "PaperNUnlock//";
                    document.getElementById("btnCallVB").click();
                }

                if (lnMinutes > 0) {
                    $("dispRemainM").value = Number($("frmLeftMinutes").value) - 1;
                    InitST();
                }
                //点名
                if (lnMinutes > 3) {
                    var call_minutes = Number(document.getElementById("frmcall_minutes").value);
                    if (call_minutes != 0 && ((totalExmMinutes - lnMinutes) % call_minutes == 0)) {
                        showCall_Inl("请在30秒的时间内，点击【确定】或【关闭】按钮，否则，本次考试将自动交卷！");
                    }
                }

                //摄像
                if (lnMinutes > 3) {

                    var CameraSum = jQuery("#hidCameraSum").val()
                    if (CameraSum == "0") {
                        var p_Camera_minutes = Number(jQuery("#hidCamera_minutes").val());
                        var min = Math.floor(p_Camera_minutes * 0.6);
                        var max = Math.floor(p_Camera_minutes / 0.6);
                        //                        alert(getRandomInt(min, max));
                        jQuery("#hidCamera_minutes").val(getRandomInt(min, max));
                        jQuery("#hidCameraSum").val("1")
                    }

                    var Camera_minutes = Number(jQuery("#hidCamera_minutes").val());

                    if (Camera_minutes != 0 && ((totalExmMinutes - lnMinutes) % Camera_minutes == 0)) {

                        //                        if (document.getElementById("frmPaperLock").value == "True") {//抓拍命令

                        //                            document.getElementById("txtCommand").value = "takephoto//" +lcfrmstuno+"//"+ Exmclsid;
                        //                            document.getElementById("btnCallVB").click();

                        //                        }
                        //                        else {
                        toCamera();
                        //                        }
                    }
                }
            }
        }

        function getRandomInt(min, max) {
            return Math.floor(Math.random() * (max - min + 1)) + min;
        }

        //====【摄像】开始=======
        function showCamera() {
            var examtype = jQuery("#frmType").val();
            var Camera_minutes = jQuery("#hidCamera_minutes").val();

            if (examtype == "1" && Camera_minutes != "0") {
                //                if (document.getElementById("frmPaperLock").value == "True") {
                //                
                //                    document.getElementById("txtCommand").value = "Initcam";
                //                    document.getElementById("btnCallVB").click();
                //                    
                //                }
                //                else {
                Camera_Inl();
                window.setTimeout("ckStream()", 2000); //1秒后，再检测摄像头
                //                }
            }
        }
        //考试锁屏时，考试服务检测摄像头错误时，弹出遮罩层，提示，摄像头出错。
        function ckStream_lock() {
            var h = jQuery(document).height();
            jQuery('#screen').css({ 'height': h });
            jQuery('#screen').show();
            jQuery('#Camera_popbox').show();
            jQuery('.Camera_mainlist').html("摄像头已断开，请检查您的摄像设备。");
        }

        //考试锁屏时，调试好摄像头，点确定，重新检查，
        function ckCamInit_lock() {
            document.getElementById("txtCommand").value = "Initcam";
            document.getElementById("btnCallVB").click();

            jQuery('#screen').hide();
            jQuery('#Camera_popbox').hide();
        }

        function Camera_Inl() {
            jQuery('#video1').show();
            var v1 = document.getElementById("video1");
            var constraints = {
                video: { width: 800, height: 600 },
                audio: false
            };

            if (navigator.mediaDevices == undefined) {
                alert('获取本地摄像头失败：请使用非IE内核的浏览器，推荐使用谷歌浏览器！');
                return false;
            }

            navigator.mediaDevices
                .getUserMedia(constraints)
                .then(function(stream) {
                    v1.srcObject = stream;
                    mediaTrack = stream.getTracks()[0];
                    v1.onloadedmetadata = function(e) {
                        v1.play();
                    };

                })
            //                .catch(function (error) {
            //                });
        }
        //否则，弹出遮罩层，提示，调试好摄像头（每秒检测摄像头一次），当调试好时，遮罩层消失。
        function ckStream() {
            if (mediaTrack && mediaTrack.readyState == "live") {
                jQuery('.popbox').find("h2").show();
                jQuery('.popbox').find('.popboxtr1').show();
                jQuery('#screen').hide();
                jQuery('.popbox').hide();
                jQuery('.popbox').height(180);
                window.setTimeout("ckStream()", 1000); //1秒后，再检测摄像头
            }
            else {
                var h = jQuery(document).height();
                jQuery('#screen').css({ 'height': h });
                jQuery('#screen').show();
                //jQuery('.popbox').center();

                jQuery('.popbox').height(80);
                jQuery('.popbox').show();
                jQuery('.popbox').find("h2").hide();
                jQuery('.popbox').find('.popboxtr1').hide();

                jQuery('.mainlist').html("摄像头已断开，请检查您的摄像设备。");

                Camera_Inl();
                window.setTimeout("ckStream()", 1000); //1秒后，再检测摄像头
            }
        }

        //检测摄像头情况，如果完好，抓拍；
        function toCamera() {
            if (mediaTrack.readyState == "live") {
                toBase64(); //摄像
            }
        }





        function toBase64() {
            var v1 = document.getElementById("video1");
            var canvas = document.createElement('canvas');
            canvas.width = 400;
            canvas.height = 300;
            var ctx = canvas.getContext('2d');
            ctx.drawImage(v1, 0, 0, canvas.width, canvas.height);

            //从画布上获取照片数据  
            var imgData = canvas.toDataURL();
            //将图片转换为Base64  
            var base64Data = imgData.substr(22);

            //            alert("base64Data:" + base64Data);

            var stu_no = jQuery("#frmStuNo").val();
            var test_id = jQuery("#frmExmClsID").val();
            var sendStr = SaveVideoCamera(test_id, stu_no, base64Data);


            if (sendStr == "ok") {
                //alert("图片上传成功");
            }
            else if (sendStr.indexOf("error|") > -1) {
                var msgs = sendStr.split('|');
                alert(msgs[1]);
            }

        }

        function SaveVideoCamera(test_id, stu_no, base64Data) {
            var lcXmlStr = "<?xml version='1.0' ?>" +
                         "<stuPicData>" +
                          "<test_id>" + test_id + "</test_id>" +
                          "<stu_no>" + stu_no + "</stu_no>" +
                          "<ImgData>" + base64Data + "</ImgData>" +
                          "</stuPicData>";
            var loXMLDoc;
            if (window.DOMParser) {
                parser = new DOMParser();
                loXMLDoc = parser.parseFromString(lcXmlStr, "text/xml");
            }
            else // Internet Explorer
            {
                loXMLDoc = new ActiveXObject("Microsoft.XMLDOM");
                loXMLDoc.async = false;
                loXMLDoc.loadXML(lcXmlStr);
            }

            var xmlHTTP;
            if (window.XMLHttpRequest) {
                xmlHTTP = new XMLHttpRequest();
            }
            else {
                xmlHTTP = new ActiveXObject("Microsoft.XMLHTTP");
            }

            xmlHTTP.open("Post", "aspAjax/ajaxSaveVideoCamera.aspx?n=" + Math.random(), false);
            xmlHTTP.send(loXMLDoc);
            return (xmlHTTP.responseText);

        }

        //---【摄像】结束--------------------

        //===【点名】开始===========
        var call_Inl;
        function showCall_Inl(msg) {
            call_Inl = window.setTimeout("closeExam();", 30000);

            var h = jQuery(document).height();
            jQuery('#screen').css({ 'height': h });
            jQuery('#screen').show();
            //            jQuery('.popbox').center();
            jQuery('.popbox').show();
            jQuery('.mainlist').html(msg);
            return false;
        }
        //点名操作
        function closeExam() {
            window.clearInterval(tid);
            window.clearInterval(intervalId);
            showMsg("规定的时间内，您没有完成点名操作，本次考试将自动交卷！");
            window.setTimeout("JudgeUploadPaper()", 3000);
            document.getElementById("txtCommand").value = "PaperNUnlock//";
            document.getElementById("btnCallVB").click();
        }
        //---【点名】结束--------------------

        function showMsg(msg) {
            window.setTimeout("closeMsg();", 3000);

            var h = jQuery(document).height();
            jQuery('#screen').css({ 'height': h });
            jQuery('#screen').show();
            //            jQuery('.popbox').center();
            jQuery('.popbox').show();
            jQuery('.mainlist').html(msg);
            return false;
        }
        function closeMsg() {
            jQuery('.close-btn').click();
        }

        function InitST() {
            jQuery("#spanS").html("59");
        }
        function SecondTicker() {
            var s = parseInt(jQuery("#spanS").html());
            if (s > 0) {
                jQuery("#spanS").html((s - 1) + "");
            }
        }


        function loadXMLDoc(pcExmClsId, pcStuNo) {

            var lcXmlStr = "<?xml version='1.0' ?>" +
                         "<time>" +
                          "<ExmClsId>" + pcExmClsId + "</ExmClsId>" +
                          "<ExmStuNo>" + pcStuNo + "</ExmStuNo>" +
                         "</time>";
            var loXMLDoc;
            if (window.DOMParser) {
                parser = new DOMParser();
                loXMLDoc = parser.parseFromString(lcXmlStr, "text/xml");
            }
            else // Internet Explorer
            {
                loXMLDoc = new ActiveXObject("Microsoft.XMLDOM");
                loXMLDoc.async = false;
                loXMLDoc.loadXML(lcXmlStr);
            }


            var xmlHTTP;
            if (window.XMLHttpRequest) {
                xmlHTTP = new XMLHttpRequest();
            }
            else {
                xmlHTTP = new ActiveXObject("Microsoft.XMLHTTP");
            }

            xmlHTTP.open("Post", "Students/XMLRequest/XMLTime.aspx", false);
            xmlHTTP.send(loXMLDoc);
            return (xmlHTTP.responseText);

        }

        function ChkStuFolder(pcPath) {
            var bs = document.getElementById("bs");
            if (bs.DtDir(pcPath, 1) == true) {
                return true;
            }
            else {
                return false;
            }
        }

        function qstart(pCztNo, pType, pFile) {
            var paper1 = document.getElementById("paper1");
            var myError = new Error();
            if (false) {
                alert("请先保存答题结果并关闭打开着的答题窗口,然后才能打开另一题!");
                paper1.ActivateWin();
            }
            else {
                var lcStr;
                var lcPath;
                lcPath = document.getElementById("WS_DIR2").value;
                if (lcPath.substring(lcPath.length - 1, lcPath.length) != "\\") {
                    lcPath = lcPath + "\\";
                }
                lcPath = lcPath + document.getElementById("frmStuNo").value;
                lcStr = lcPath + "\\" + pFile.replace(";", "\\");

                if (pType == 21) {
                    if (pFile == "") {
                        paper1.OpenExplorer(lcPath);
                    }
                    else {
                        paper1.OpenExplorer(lcStr);
                    }
                }
                else if (pType == 32) {
                    paper1.OpenNotePad(lcStr.toLowerCase());
                    if (myError.number != 0) {
                        alert("记事本打开错误");
                    }
                }
                else if (pType == 22) {
                    paper1.OpenWord(lcStr);
                    if (myError.number != 0) {
                        alert("Word无法打开，可能是安装有问题！");
                    }
                }
                else if (pType == 23) {
                    paper1.OpenExcel(lcStr);
                    if (myError.number != 0) {
                        alert("Excel无法打开，可能是安装有问题！");
                    }
                }
                else if (pType == 24) {
                    if (pFile == "") {
                        paper1.OpenVfp(lcPath + "\foxkt");
                    }
                    else {
                        paper1.OpenVfp(lcStr);
                    }
                    if (myError.number != 0) {
                        alert("VFP无法打开，可能是安装有问题！");
                    }
                }
                else if (pType == 27) {
                    paper1.OpenPpt(lcStr);
                    if (myError.number != 0) {
                        alert("PowerPoint无法打开，可能是安装有问题！");
                    }
                }
                else if (pType == 28) {
                    paper1.OpenWps(lcStr);
                    if (myError.number != 0) {
                        alert("Wps无法打开，可能是安装有问题！");
                    }
                }
                else if (pType == 25) {
                    var lcStr1 = window.event.srcElement.name;
                    var lcQstNo = lcStr1.substring(1, lcStr1.length);
                    window.open(pFile + "?QstNo=" + lcQstNo + "&StuNo=" + document.getElementById("StuNo").value + "&ExmNo=" + document.getElementById("frmUpExmNo").value)
                }
                else if (pType == 35) {
                    var lcStr1 = window.event.srcElement.name;
                    var lcQstNo = lcStr1.substring(1, lcStr1.length);
                    window.open("wmLogin.aspx" + "?QstNo=" + lcQstNo + "&StuNo=" + document.getElementById("StuNo").value + "&ExmNo=" + document.getElementById("frmUpExmNo").value)
                }
                else if (pType == 26) {
                    paper1.OpenOE(lcStr);
                    if (myError.number != 0) {
                        alert("OutLook Express考试程序无法打开");
                    }
                }
                else if (pType == 36) {
                    var strUrl;
                    strUrl = window.location.href;
                    paper1.OpenTopIE(lcStr, strUrl);
                    if (myError.number != 0) {
                        alert("IE浏览考试程序无法打开！");
                    }
                }
                else {
                    if (pFile == "") {
                        paper1.OpenExplorer(lcPath);
                    }
                    else {
                        paper1.OpenExplorer(lcStr);
                    }
                }
                for (var i = 1; i < document.getElementById("CztNum").value; i++) {
                    document.getElementById("CZT" + i).style.backgroundColor = "";

                }

                document.getElementById("CZT" + pCztNo).style.backgroundColor = "#ecfff2";

                var qnum = $("CZT" + pCztNo).parentElement.id;
                var ids = "#divloadnum" + qnum;
                jQuery(ids).removeClass("questionload").addClass("questionload1");
                if (jQuery(ids).hasClass("questionload_border")) {
                    jQuery(ids).removeClass("questionload_border").addClass("questionload_bordergreen");
                }


            }
        }

        function Form_OnSubmit() {
            var paper1 = document.getElementById("paper1");
            if (false) {
                alert("请先保存答题结果并关闭打开着的答题窗口!");
                paper1.ActivateWin();
                paper1.SendCloseMessageToWin();
                return false;
            }
            else {
                return true;
            }
        }

        function questionload(quesionnum, loadgroupnum, loadgroupname) {
            var htmlstr = "";
            var num = 0;


            var loadgroupnums = loadgroupnum.split('|');
            var loadgroupnames = loadgroupname.split('|');

            var arrnum = 0;
            var qnum = parseInt(loadgroupnums[arrnum]);

            htmlstr = "<div   style=\"clear:both;width: 220px;height:16px;line-height:16px; font-size:14px; background:#fff; border-left:3px solid #1ca53c;margin:10px auto; padding-left:10px;\">" + loadgroupnames[arrnum] + "</div><div style=\"margin:10px auto; overflow:hidden;\"    >";

            arrnum = arrnum + 1;
            qnum = parseInt(loadgroupnums[arrnum]);
            if (quesionnum != "") {
                var count = parseInt(quesionnum);
                for (var i = 1; i <= count; i++) {
                
 
                

                    if (i == qnum && i > 1) {
                        htmlstr += "</div><div   style=\"clear:both;width: 220px;height:16px;line-height:16px; font-size:14px; background:#fff; border-left:3px solid #1ca53c;margin:10px auto; padding-left:10px;\">" + loadgroupnames[arrnum] + "</div><div style=\"margin:10px auto; overflow:hidden;\"    >";

                        arrnum = arrnum + 1;
                        qnum = parseInt(loadgroupnums[arrnum]);
                    }
                    var idnum = "#biaoji" + i;
                    var qstno = jQuery(idnum).attr("QstNo");
                    var lcstr = "DIV" + qstno;

                    if (document.getElementById(lcstr) == null || document.getElementById(lcstr) == undefined || (document.getElementById(lcstr).style.backgroundColor != "#ecfff2" && document.getElementById
(lcstr).style.backgroundColor != "rgb(236, 255, 242)")) {
                        //htmlstr += "<div   id=\"divloadnum" + i + "\" class=\"questionload questionload_border\"><a href=\"#aname" + i + "\">" + (i - num) + "</a></div>";
                        htmlstr += "<div   id=\"divloadnum" + i + "\" class=\"questionload questionload_border\"><a href=\"javascript:void(0)\" onclick=\"openqst(" + i + ")\">" + (i - num) + "</a></div>";
                    }
                    else {
                        //htmlstr += "<div   id=\"divloadnum" + i + "\" class=\"questionload1 questionload_bordergreen\"><a href=\"#aname" + i + "\">" + (i - num) + "</a></div>";
                        htmlstr += "<div   id=\"divloadnum" + i + "\" class=\"questionload1 questionload_bordergreen\"><a href=\"javascript:void(0)\" onclick=\"openqst(" + i + ")\">" + (i - num) + "</a></div>";
                    }

                }
                htmlstr += "</div>";

            }
            document.getElementById("msg1").innerHTML = htmlstr;

            if (document.documentElement.clientHeight - 400 < document.getElementById("msg1").scrollHeight) {
                document.getElementById("msg1").style.height = (document.documentElement.clientHeight - 400) + "px";
                document.getElementById("msg1").style.overflow = "auto";
            }

        }
        function hiddenSC() {
            if (jQuery("#frmType").val() == "1") {
                jQuery("[names='shoucang']").hide();
                jQuery("[names='jiucuo']").hide();
            }
        }




        function RefreshAsw(pcQstNo, pnQstType, pcAsw) {
            var lnI;
            var a = "A";
            if (pcAsw == "") {
                return;
            }
            lcStr = "A" + pcQstNo;
            if (pnQstType == 1) {
                var t = jQuery("[name='" + "A" + pcQstNo + "']");
                for (var i = 0; i < t.length; i++) {
                    if (t[i].value == pcAsw) {
                        t[i].checked = true;
                    }
                }
            }
            else if (pnQstType == 2) {
                var t = jQuery("[name='" + "A" + pcQstNo + "']");
                for (var i = 0; i < t.length; i++) {
                    if (t[i].value == pcAsw) {
                        t[i].checked = true;
                    }
                }
            }
            else if (pnQstType == 3) {
                var lcAsw = pcAsw;
                lnI = 1;
                while (lcAsw != "") {
                    var lcChar = lcAsw.substr(0, 1); var t = jQuery("[name='" + "A" + pcQstNo + "']");
                    for (var i = 0; i < t.length; i++) {
                        if (t[i].value == lcChar) {
                            t[i].checked = true;
                        }
                    }
                    lcAsw = lcAsw.substring(1, lcAsw.length);
                }
            }
            else if (pnQstType == 4) {
                var lcAsw = pcAsw;
                lnI = 1;
                while (lcAsw != "") {
                    var lcChar = lcAsw.substr(0, 1); var t = jQuery("[name='" + "A" + pcQstNo + "']");
                    for (var i = 0; i < t.length; i++) {
                        if (t[i].value == lcChar) {
                            t[i].checked = true;
                        }
                    }
                    lcAsw = lcAsw.substring(1, lcAsw.length);
                }
            }
            else if (pnQstType == 5) {
                if (pcAsw.toUpperCase() == "YES") {
                    jQuery("[name='" + lcStr + "']")[0].checked = true;
                }
                else if (pcAsw.toUpperCase() == "NO") {
                    jQuery("[name='" + lcStr + "']")[1].checked = true;
                }
            }
            else if (pnQstType == 6) {
                var lcStr = "F" + pcQstNo;
                var lcAsw = "";
                lcAsw = pcAsw;
                for (var i = 1; i < 1000; i++) {
                    var lnPos = lcAsw.indexOf("//");
                    if (lnPos > 0) {
                        debugger;
                        document.getElementById(lcStr + "_" + i).value = TieBlank(lcAsw.substr(0, lnPos - 1));
                        lcAsw = lcAsw.substring(lnPos + 2, lcAsw.length);
                    }
                    else {
                        document.getElementById(lcStr + "_" + i).value = TieBlank(lcAsw.substr(0, lcAsw.length - 1));
                        lcAsw = "";
                        break;
                    }
                }
            }
            else if (pnQstType == 7) {
                var lcAsw = pcAsw;
                lcStr = "T" + pcQstNo;
                document.getElementById(lcStr).value = lcAsw;
            }
            else if (pnQstType == 8) {
                var lcStr = "F" + pcQstNo;
                var lcAsw = "";
                lcAsw = pcAsw;
                var ss = new Array();
                ss = pcAsw.split("//");
                for (var i = 0; i < ss.length; i++) {
                    var t = document.getElementById(lcStr + "_" + (i + 1));
                    for (var y = 0; y < t.options.length; y++) {
                        var ssstr = ss[i].replace(/\s+/g, "");
                        if (ssstr == t.options[y].value) {
                            t.options[y].selected = true;
                        }
                    }
                }
            }
            if (pcAsw != "") {
                document.getElementById("DIV" + lcStr.substring(1, lcStr.length)).style.backgroundColor = "#ecfff2";


                var qnum = $("DIV" + lcStr.substring(1, lcStr.length)).parentElement.id;
                var ids = "#divloadnum" + qnum;
                jQuery(ids).removeClass("questionload").addClass("questionload1");
                if (jQuery(ids).hasClass("questionload_border")) {
                    jQuery(ids).removeClass("questionload_border").addClass("questionload_bordergreen");
                }
            }
        }

        //        function TieBlank(lcStrBlank) {
        //           
        //            lcStrBlank = jQuery.trim(lcStrBlank);
        //            var reg = new RegExp("\\\\/", "g");
        //            lcStrBlank = lcStrBlank.replace(reg, "/");
        //            return lcStrBlank;
        //        }
        //获取时间
        function GetTime() {
            var hh;
            var MM;
            var ss;
            var date = new Date();
            hh = date.getHours();
            if (hh < 10) {
                hh = "0" + hh;
            }
            MM = date.getMinutes();
            if (MM < 10) {
                MM = "0" + MM;
            }
            ss = date.getSeconds();
            if (ss < 10) {
                ss = "0" + ss;
            }
            var result = hh + ":" + MM + ":" + ss;

            return result;
        }
        //获取日期加时间
        function GetTime() {
            var year;
            var month;
            var day;
            var hh;
            var MM;
            var ss;
            var date = new Date();
            year = date.getFullYear();
            month = date.getMonth() + 1;
            if (month < 10) {
                month = "0" + month;
            }
            day = date.getDate();
            if (day < 10) {
                day = "0" + day;
            }
            hh = date.getHours();
            if (hh < 10) {
                hh = "0" + hh;
            }
            MM = date.getMinutes();
            if (MM < 10) {
                MM = "0" + MM;
            }
            ss = date.getSeconds();
            if (ss < 10) {
                ss = "0" + ss;
            }
            var result = hh + ":" + MM + ":" + ss;

            return result;
        }

        function DispSegment(pnSegNo, pnFlag) {
            //SetSegment(pnSegNo, "none");
            var lnFirst;
            var lnLast;
            var isHidden = true;

            var trobj = jQuery("#papertable  table tr:not(.bp):eq(0)").nextAll(); //不包括第一个tr的jquery集合，也就是说全部tr少第一个。
            //            if (trobj.eq(0).find('h3').length == 1) {
            //                trobj = jQuery("#papertable  table tr:eq(1)").nextAll();
            //            }
            var hasH3 = jQuery("#papertable table tr:has(h3)");
            if (pnSegNo < 0 || pnSegNo > hasH3.size()) {
                alert("大题号超出范围！");
                return;
            } else {
                if (pnSegNo < hasH3.size()) {
                    if (pnSegNo == 1) {
                        lnFirst = 0;
                        lnLast = trobj.index(jQuery("#papertable table tr:has(h3):eq(" + pnSegNo + ")"));
                    }
                    else {
                        var i1 = pnSegNo - 1;
                        lnFirst = trobj.index(jQuery("#papertable table tr:has(h3):eq(" + i1 + ")"));
                        lnLast = trobj.index(jQuery("#papertable table tr:has(h3):eq(" + pnSegNo + ")"));
                    }

                }
                else {
                    var i2 = pnSegNo - 1;
                    lnFirst = trobj.index(jQuery("#papertable table tr:has(h3):eq(" + i2 + ")"));
                    lnLast = -1;
                }

                if (lnLast != -1) {
                    if (pnSegNo == 1) {
                        trobj.slice(lnFirst, lnLast).toggle();
                        isHidden = trobj.slice(lnFirst, 1).is(":hidden");
                    } else {
                        trobj.slice(lnFirst + 1, lnLast).toggle();
                        isHidden = trobj.slice(lnFirst, lnLast).is(":hidden");
                    }
                }
                else {
                    trobj.slice(lnFirst + 1).toggle();
                    isHidden = trobj.slice(lnFirst).is(":hidden")
                }
            }

            jQuery(".bp").show();
            if (!isHidden) {
                document.getElementById("node" + pnSegNo).src = "images/minus.gif";
            }
            else {
                document.getElementById("node" + pnSegNo).src = "images/add.gif";
            }
        }

        function SetSegment(pnSegNo, pcValue) {

            var lnFirst;
            var lnLast;
            var ArrQst = new Array();
            var lcQIndex = document.getElementById("QINDEX").value;
            var ArrQIndex = lcQIndex.split(",");
            var j = ArrQIndex.length / 3;
            if (j <= 0) {
                alert("错误：QINDEX中的值为空！");
                return;
            }
            for (var i = 0; i < j; i++) {
                ArrQst[i] = ArrQIndex[i * 3];
            }

            var lcSegment = document.getElementById("SEGMENT").value;
            var ArrSegment = lcSegment.split("//");
            if (pnSegNo < 0 || pnSegNo > ArrSegment.length) {
                alert("大题号超出范围！");
                return;
            }
            else {
                if (pnSegNo < ArrSegment.length) {
                    lnFirst = ArrSegment[pnSegNo - 1];
                    lnLast = ArrSegment[pnSegNo] - 1;
                }
                else {
                    lnFirst = ArrSegment[pnSegNo - 1];
                    lnLast = j;
                }
            }

            var lcValue = "";
            for (var i = lnFirst; i <= lnLast; i++) {
                var lcQstSno = "tr" + ArrQst[i - 1];
                if (lcValue == "") {
                    if (document.getElementById(lcQstSno).style.display == "none") {
                        lcValue = "block";
                    }
                    else {
                        lcValue = "none";
                    }
                }
                document.getElementById(lcQstSno).style.display = lcValue;
            }
            if (lcValue == "block") {
                document.getElementById("node" + pnSegNo).src = "images/minus.gif";
            }
            else {
                document.getElementById("node" + pnSegNo).src = "images/add.gif";
            }
        }
        //        function FormatToSQLD(str) {
        //            str = str.replace(/&/, "&amp;");
        //            str = str.replace(/'/g, "''");
        //            str = str.replace(/"/g, "&quot;");
        //            str = str.replace(/ /g, "&nbsp;");
        //            str = str.replace(/</g, "&lt;");
        //            str = str.replace(/>/g, "&gt;");
        //            str = str.replace(/\n/g, "<br>");
        //            return str;
        //        }

        function filterBlank(lcStrBlank) {

            //            var reg = new RegExp("/", "g");
            //            lcStrBlank = lcStrBlank.replace(reg, "\\/");
            return " " + lcStrBlank + " ";
        }

        function Getplaytimes(pcExmClsId, pcStuNo, pcQstNo) {
            var lcXmlStr = "<?xml version='1.0' ?>\n" +
                       "<playtime>\n" +
                        "<ExmClsId>" + pcExmClsId + "</ExmClsId>\n" +
                        "<ExmStuNo>" + pcStuNo + "</ExmStuNo>\n" +
                        "<ExmQstNo>" + pcQstNo + "</ExmQstNo>\n" +
                        "</playtime>";

            var loXMLDoc;
            if (window.DOMParser) {
                parser = new DOMParser();
                loXMLDoc = parser.parseFromString(lcXmlStr, "text/xml");
            }
            else // Internet Explorer
            {
                loXMLDoc = new ActiveXObject("Microsoft.XMLDOM");
                loXMLDoc.async = false;
                loXMLDoc.loadXML(lcXmlStr);
            }


            var xmlHTTP;
            if (window.XMLHttpRequest) {
                xmlHTTP = new XMLHttpRequest();
            }
            else {
                xmlHTTP = new ActiveXObject("Microsoft.XMLHTTP");
            }

            xmlHTTP.open("POST", "Students/XMLRequest/XMLGetPlayTimes.aspx", false);
            xmlHTTP.send(loXMLDoc);
            var res = xmlHTTP.responseText;

            return res;

        }

        function ServerIsOK() {
            var lcXmlStr = "<?xml version='1.0' ?>\n" +
                       "<site><serverOK>OK</serverOK></site>";
            var loXMLDoc;
            if (window.DOMParser) {
                parser = new DOMParser();
                loXMLDoc = parser.parseFromString(lcXmlStr, "text/xml");
            }
            else // Internet Explorer
            {
                loXMLDoc = new ActiveXObject("Microsoft.XMLDOM");
                loXMLDoc.async = false;
                loXMLDoc.loadXML(lcXmlStr);
            }


            var xmlHTTP;
            if (window.XMLHttpRequest) {
                xmlHTTP = new XMLHttpRequest();
            }
            else {
                xmlHTTP = new ActiveXObject("Microsoft.XMLHTTP");
            }
            xmlHTTP.open("POST", "Students/XMLRequest/XMLValidate.aspx", false);
            xmlHTTP.send(loXMLDoc);
            var res = xmlHTTP.responseText;

            if (res != "") {
                alert("错误：考试服务器连接失败！");
            }

        }
        function XMLSendToServer() {

            var lcStuNo = document.getElementById("frmStuNo").value;
            var lcExmClsId = document.getElementById("frmExmClsID").value;
            var lcXmlStr = "<?xml version='1.0' ?>\n" +
                        "<sendPaper>\n" +
                        "<ExmClsId>" + lcExmClsId + "</ExmClsId>\n" +
                        "<ExmStuNo>" + lcStuNo + "</ExmStuNo>\n" +
                        "</sendPaper>";

            var loXMLDoc;
            if (window.DOMParser) {
                parser = new DOMParser();
                loXMLDoc = parser.parseFromString(lcXmlStr, "text/xml");
            }
            else // Internet Explorer
            {
                loXMLDoc = new ActiveXObject("Microsoft.XMLDOM");
                loXMLDoc.async = false;
                loXMLDoc.loadXML(lcXmlStr);
            }


            var xmlHTTP;
            if (window.XMLHttpRequest) {
                xmlHTTP = new XMLHttpRequest();
            }
            else {
                xmlHTTP = new ActiveXObject("Microsoft.XMLHTTP");
            }

            xmlHTTP.open("POST", "Students/XMLRequest/XMLUpLoadPaper.aspx", false);
            xmlHTTP.send(loXMLDoc);
            var res = xmlHTTP.responseText;

            return res;
        }
        function checkabandoned() {

            var lcStuNo = document.getElementById("frmStuNo").value;
            var lcExmClsId = document.getElementById("frmExmClsID").value;
            var lcXmlStr = "<?xml version='1.0' ?>\n" +
                        "<sendPaper>\n" +
                        "<ExmClsId>" + lcExmClsId + "</ExmClsId>\n" +
                        "<ExmStuNo>" + lcStuNo + "</ExmStuNo>\n" +
                        "</sendPaper>";

            var loXMLDoc;
            if (window.DOMParser) {
                parser = new DOMParser();
                loXMLDoc = parser.parseFromString(lcXmlStr, "text/xml");
            }
            else // Internet Explorer
            {
                loXMLDoc = new ActiveXObject("Microsoft.XMLDOM");
                loXMLDoc.async = false;
                loXMLDoc.loadXML(lcXmlStr);
            }


            var xmlHTTP;
            if (window.XMLHttpRequest) {
                xmlHTTP = new XMLHttpRequest();
            }
            else {
                xmlHTTP = new ActiveXObject("Microsoft.XMLHTTP");
            }

            xmlHTTP.open("POST", "Students/XMLRequest/XMLcheckabandoned.aspx", false);
            xmlHTTP.send(loXMLDoc);
            var res = xmlHTTP.responseText;
            if (res == "true") {
                return true;
            }
            else {
                return false;
            }
        }

        function UplaodPaper() {

            if (checkabandoned()) {
                showMsg("您本次考试已被管理员作废！");
                //增加解屏操作
                document.getElementById("txtCommand").value = "PaperNUnlock//";
                document.getElementById("btnCallVB").click();
                if (document.getElementById("frmPaperLock").value == "True") {
                    ExitTest();
                }
                else {
                    //                    if (jQuery("#hidIsExamSys").val() == "1") {

                    window.setTimeout("gotoStuMain();", 3000);
                    //                    }
                    //                    else {

                    //                        window.setTimeout("gotoApplyverify();", 3000);
                    //                    }
                }
            }
            else {
                //         var result=JudgeUpload()
                //        if(result=="isnull"){
                document.getElementById("openWindowS").value = "1";
                ServerIsOK();
                var div_data = jQuery("#papertable").find("div[name^='DIV']"); // document.getElementsByTagName("DIV");document.getElementsByTagName("DIV");
                var count = 0;
                for (var i = 0; i < div_data.length; i++) {

                    var $obj = div_data[i];
                    if ($obj != undefined && $obj != null && $obj.style.backgroundColor != "#ecfff2" && $obj.style.backgroundColor != "rgb(236, 255, 242)") {
                        count++;
                    }
                }
                if (count == 0) {
                    lnRet = confirm("真的要交卷吗?")
                }
                else {
                    lnRet = confirm("您还有" + count + "道题未做，真的要交卷吗？");
                }
                if (lnRet == true) {
                    lnRet = confirm("真的要交卷吗,请再次确认?");
                    if (lnRet == true) {
                        JudgeUploadPaper();
                    }
                    else {
                        document.getElementById("openWindowS").value = "0";
                    }
                    //增加解屏操作
                    document.getElementById("txtCommand").value = "PaperNUnlock//";
                    document.getElementById("btnCallVB").click();
                }
                else {
                    document.getElementById("openWindowS").value = "0";
                }
                //            }
                //            else{
                //                alert("你已被“强制交卷”，不允许再次交卷");
                //                window.close();
                //            }
            }
        }
        function JudgeUploadPaper() {
            var lcresult = XMLSendToServer();
            if (lcresult != "") {
                alert(lcresult);
                return;
            }
            else {
                if (document.getElementById("frmUpExmAllowJudge").value.toUpperCase() == "TRUE") {
                    gotoPsaveN(1);
                }
                else {

                    if (document.getElementById("frmPaperLock").value == "True") {
                        //ExitTest();
                        gotoPsaveN(3);
                    }
                    else {
                        gotoPsaveN(0);
                        //                        if (jQuery("#hidIsExamSys").val() == "1") {
                        //                            gotoStuMain();
                        //                        }
                        //                        else {
                        //                            gotoApplyverify();
                        //                        }
                    }
                }
            }
        }
        function ExitTest() {
            document.getElementById("txtCommand").value = "ExitTestWindow//";
            document.getElementById("btnCallVB").click();
        }
        function JudgeUpload() {
            var lcStuNo = document.getElementById("frmStuNo").value;
            var lcExmClsId = document.getElementById("frmExmClsID").value;
            var lcXmlStr = "<?xml version='1.0' ?>\n" +
                        "<upload>\n" +
                        "<ExmClsId>" + lcExmClsId + "</ExmClsId>\n" +
                        "<ExmStuNo>" + lcStuNo + "</ExmStuNo>\n" +
                        "</upload>";

            var loXMLDoc;
            if (window.DOMParser) {
                parser = new DOMParser();
                loXMLDoc = parser.parseFromString(lcXmlStr, "text/xml");
            }
            else // Internet Explorer
            {
                loXMLDoc = new ActiveXObject("Microsoft.XMLDOM");
                loXMLDoc.async = false;
                loXMLDoc.loadXML(lcXmlStr);
            }


            var xmlHTTP;
            if (window.XMLHttpRequest) {
                xmlHTTP = new XMLHttpRequest();
            }
            else {
                xmlHTTP = new ActiveXObject("Microsoft.XMLHTTP");
            }

            xmlHTTP.open("POST", "Students/XMLRequest/XMLJudgeUpload.aspx", false);
            xmlHTTP.send(loXMLDoc);
            var res = xmlHTTP.responseText;

            return res;
        }

        function saveanws(pQstType, event) {
            var evt = event || window.event;
            var code = window.event ? evt.srcElement : evt.target;

            var lcStr = code.name;
            var lcSrc = code.name;

            var lnLen = lcStr.length;

            lcStr = lcStr.substring(0, lnLen - 1);

            var lnLent = lcSrc.length;

            lcSrc = lcSrc.substring(0, lnLent - 1);

            var lcQstNo = lcStr.substring(1, lcStr.length);

            var lcQIndex = document.getElementById("QINDEX").value;
            var lnItemNum = 0;
            while (lcQIndex != "") {
                var lnLen = lcQIndex.length;
                var lnPos1 = lcQIndex.indexOf(",");
                var lnPos2 = lcQIndex.indexOf(",", lnPos1 + 1);
                var lnPos3 = lcQIndex.indexOf(",", lnPos2 + 1);

                if (lcQstNo == lcQIndex.substring(0, lnPos1)) {
                    var lcQIndex1;
                    lcQIndex1 = lcQIndex.substring(0, lnPos2);
                    var lnQstType = parseInt(lcQIndex1.substring(lcQIndex1.indexOf(",") + 1, lcQIndex1.length));
                    lcQIndex1 = lcQIndex.substring(0, lnPos3);
                    lnItemNum = parseInt(lcQIndex1.substring(lcQIndex1.lastIndexOf(",") + 1, lcQIndex1.length));
                    lcQIndex = "";
                }
                else {
                    lcQIndex = lcQIndex.substring(lcQIndex.length - (lnLen - lnPos3) + 1, lcQIndex.length);
                }
            }

            var obj;
            var lcResult = "";

            if (pQstType == 6)//填空
            {
                for (var i = 1; i <= lnItemNum; i++) {
                    lcResult = lcResult + filterBlank(document.getElementById(lcStr + "_" + i).value);

                    if (i < lnItemNum) {
                        lcResult = lcResult + "//";
                    }
                }
            }
            else if (pQstType == 7) {
                // lcResult = lcResult + document.getElementByName(lcStr).value;
                obj = jQuery("[name='" + lcSrc + "']");

                for (var i = 0; i < obj.length; i++) {
                    lcResult = lcResult + obj[i].value;

                }
            }

            var lcStuNo = document.getElementById("StuNo").value;
            var lcExmNo = document.getElementById("ExmNO").value;
            var lcExmClsId = document.getElementById("frmExmClsID").value;
            sendAswToServer(lcExmClsId, lcStuNo, lcQstNo, lcResult, lcStr);

        }

        function divclicks(pQstType, event) {
            //            var result = JudgeUpload()
            //            if (result == "isnull") {
            var evt = event || window.event;
            var code = window.event ? evt.srcElement : evt.target;

            var lcStr = code.name;
            var lcSrc = code.name;


            if (pQstType == 6 || pQstType == 8) {
                var lnLen = lcStr.length;
                var index = lcStr.lastIndexOf("_");
                lcStr = lcStr.substring(0, index);
            }
            var lcQstNo = lcStr.substring(1, lcStr.length);
            var lcQIndex = document.getElementById("QINDEX").value;
            var lnItemNum = 0;
            while (lcQIndex != "") {
                var lnLen = lcQIndex.length;
                var lnPos1 = lcQIndex.indexOf(",");
                var lnPos2 = lcQIndex.indexOf(",", lnPos1 + 1);
                var lnPos3 = lcQIndex.indexOf(",", lnPos2 + 1);

                if (lcQstNo == lcQIndex.substring(0, lnPos1)) {
                    var lcQIndex1;
                    lcQIndex1 = lcQIndex.substring(0, lnPos2);
                    var lnQstType = parseInt(lcQIndex1.substring(lcQIndex1.indexOf(",") + 1, lcQIndex1.length));
                    lcQIndex1 = lcQIndex.substring(0, lnPos3);
                    lnItemNum = parseInt(lcQIndex1.substring(lcQIndex1.lastIndexOf(",") + 1, lcQIndex1.length));
                    lcQIndex = "";
                }
                else {
                    lcQIndex = lcQIndex.substring(lcQIndex.length - (lnLen - lnPos3) + 1, lcQIndex.length);
                }
            }

            var obj;
            var lcResult = ""; //答题结果
            if (pQstType == 1)//单选
            {
                obj = jQuery("[name='" + lcSrc + "']");
                for (var i = 0; i < obj.length; i++) {
                    if (obj[i].checked) {
                        lcResult = lcResult + obj[i].value;
                    }
                }
            }
            else if (pQstType == 2)//单选2
            {
                obj = jQuery("[name='" + lcSrc + "']");
                for (var i = 0; i < obj.length; i++) {
                    if (obj[i].checked) {
                        lcResult = lcResult + obj[i].value;
                    }
                }
            }
            else if (pQstType == 3)//多选
            {
                obj = jQuery("[name='" + lcSrc + "']");
                for (var i = 0; i < obj.length; i++) {
                    if (obj[i].checked) {
                        lcResult = lcResult + obj[i].value;
                    }
                }
                lcResult = setOrderResult(lcResult);
            }
            else if (pQstType == 4)//多选2
            {
                obj = jQuery("[name='" + lcSrc + "']");
                for (var i = 0; i < obj.length; i++) {
                    if (obj[i].checked) {
                        lcResult = lcResult + obj[i].value;
                    }
                }
                lcResult = setOrderResult(lcResult);

            }
            else if (pQstType == 5)//判断
            {
                obj = jQuery("[name='" + lcSrc + "']");
                for (var i = 0; i < obj.length; i++) {
                    if (obj[i].checked) {
                        lcResult = lcResult + obj[i].value;
                    }
                }
            }
            else if (pQstType == 6)//填空
            {
                for (var i = 1; i <= lnItemNum; i++) {
                    lcResult = lcResult + filterBlank(document.getElementById(lcStr + "_" + i).value);

                    if (i < lnItemNum) {
                        lcResult = lcResult + "//";
                    }
                }
            }
            else if (pQstType == 7) {
                // lcResult = lcResult + document.getElementByName(lcStr).value;
                obj = jQuery("[name='" + lcSrc + "']");
                for (var i = 0; i < obj.length; i++) {

                    lcResult = lcResult + obj[i].value;
                }
            }
            else if (pQstType == 8)//选择填空
            {
                for (var i = 1; i <= lnItemNum; i++) {
                    var t = document.getElementById(lcStr + "_" + i);
                    lcResult = lcResult + filterBlank(t.options[t.selectedIndex].value);
                    //                    lcResult = lcResult.replace(/\s+/g, ""); 
                    if (i < lnItemNum) {
                        lcResult = lcResult + "//";
                    }

                }
            }

            var lcStuNo = document.getElementById("StuNo").value;
            var lcExmNo = document.getElementById("ExmNO").value;
            var lcExmClsId = document.getElementById("frmExmClsID").value;
            sendAswToServer(lcExmClsId, lcStuNo, lcQstNo, lcResult, lcStr);

        }
        function sendAswToServer(pcExmClsId, pcStuNo, pcQstNo, pcAsw, lcStr) {
            var data = {
                ExmClsId: pcExmClsId,
                ExmStuNo: pcStuNo,
                ExmQstNo: pcQstNo,
                ExmAsw: pcAsw,
                n: Math.random()
            };
            jQuery.ajax({
                url: "Students/XMLRequest/XMLSaveQstAswToServer2.aspx",
                type: "post",
                data: data,
                async: false,
                dataType: "text",
                success: function(msg) {
                    if (msg == "aswOK") {
                        var diver = jQuery("#DIV" + lcStr.substring(1, lcStr.length));
                        diver.css("backgroundColor", "#ecfff2");
                        var qnum = $("DIV" + lcStr.substring(1, lcStr.length)).parentElement.id;
                        var ids = "#divloadnum" + qnum;
                        jQuery(ids).removeClass("questionload").addClass("questionload1");
                        if (jQuery(ids).hasClass("questionload_border")) {
                            jQuery(ids).removeClass("questionload_border").addClass("questionload_bordergreen");
                        }
                    }
                    else {
                        alert("试题保存失败：" + msg);

                        var diver = jQuery("#DIV" + lcStr.substring(1, lcStr.length));
                        diver.css("backgroundColor", "red");

                        var qnum = $("DIV" + lcStr.substring(1, lcStr.length)).parentElement.id;
                        var ids = "#divloadnum" + qnum;
                        jQuery(ids).css("backgroundColor", "red");

                        if (msg == "试题未找到！") {
                            alert("你已被重考处理，如有异议请联系管理员！");
                            window.history.back();
                        }
                         

                       
                        
                    }


                },
                error: function(err) {
                    alert('与服务器连接出错，请检查网络。\n注意：连接错误期间的答题无效，请重新做答背景未变色的试题。');
                }
                //error: function(XMLHttpRequest, textStatus, errorThrown) {
                //alert("status:"+XMLHttpRequest.status);
                //alert("readyState:"+XMLHttpRequest.readyState);
                //     alert("textStatus:"+textStatus);
                // },
                //                    complete: function(XMLHttpRequest, textStatus) {
                //                        this; // 调用本次AJAX请求时传递的options参数
                //                    }
            });
        }
        function setOrderResult(lcResult) {
            var a = new Array();
            for (var y = 0; y < lcResult.length; y++) {
                a[y] = lcResult.substr(y, 1);
            }
            for (var i = 0; i < a.length; i++) {
                for (var j = i + 1; j < a.length; j++) {
                    if (a[i] > a[j]) {
                        var t = a[i]
                        a[i] = a[j]
                        a[j] = t
                    }
                }
            }
            lcResult = "";
            for (var i = 0; i < a.length; i++) {
                lcResult += a[i];
            }
            return lcResult;
        }

        //        function setMPstart(pcqstno) {
        ////            var mp = jwplayer("container_" + pcqstno)
        ////            if (mp.getState().toUpperCase() == "IDLE") {
        ////                if (confirm("开始播放将不能暂停！确认要播放吗？")) {
        ////                    var pcExmClsId = document.getElementById("frmExmClsID").value
        ////                    var pcStuNo = document.getElementById("frmStuNo").value
        ////                    var filename = Getplaytimes(pcExmClsId, pcStuNo, pcqstno)
        ////                    if (filename.substring(0, 3) == "错误：") {
        ////                        alert("提取媒体文件错误！")
        ////                        return
        ////                    }
        ////                    if (filename == "") {
        ////                        alert('此媒体文件已经播放完毕！')
        ////                    }
        ////                    else {
        ////                        alert("开始播放！");
        ////                        mp.play();
        ////                    }
        ////                }
        //            //            }
        //            var mp = jQuery("#container_" + pcqstno)[0];
        //            if (mp.paused || mp.ended) {
        //                if (confirm("开始播放将不能暂停！确认要播放吗？")) {
        //                    var pcExmClsId = document.getElementById("frmExmClsID").value;
        //                    var pcStuNo = document.getElementById("frmStuNo").value;
        //                    var filename = Getplaytimes(pcExmClsId, pcStuNo, pcqstno);
        //                    if (filename.substring(0, 3) == "错误：") {
        //                        alert("提取媒体文件错误！");
        //                        return
        //                    }
        //                    if (filename == "") {
        //                        alert('此媒体文件已经播放完毕！');
        //                    }
        //                    else {
        //                        alert("开始播放！");
        //                        mp.play();
        //                    }
        //                }
        //            }
        //        }

        //        function setVolume(flag, pcqstno) {
        //            var mp = jQuery("#container_" + pcqstno)[0]
        //            var tnum = mp.volume;
        //            if (flag == 1) {
        //                tnum = tnum + 0.2;
        //            }
        //            else {
        //                tnum = tnum - 0.2;
        //            }
        //            if (tnum >= 1) { tnum = 1; }
        //            if (tnum <= 0.1) { tnum = 0.1; }
        //            mp.volume = tnum;
        //        }
        //        function setMPstart(pcqstno) {
        //            document.getElementById('BT' + pcqstno).disabled = true
        //            var mp = document.getElementById('MP' + pcqstno)
        //            mtname = mp.name
        //            pcExmClsId = document.getElementById("frmExmClsID").value
        //            pcStuNo = document.getElementById("frmStuNo").value
        //            if (mtname.substring(0, 2) == "MP") {
        //                if (mp.currentPosition != -1 && mp.currentPosition != 0) {
        //                    alert('媒体文件正在播放！')
        //                    document.getElementById('BT' + pcqstno).disabled = false
        //                    return
        //                }
        //                if (confirm("开始播放将不能暂停！确认要播放吗？")) {
        //                    filename = Getplaytimes(pcExmClsId, pcStuNo, pcqstno)
        //                    if (filename.substring(0, 3) == "错误：") {
        //                        alert("提取媒体文件错误！")
        //                        document.getElementById('BT' + pcqstno).disabled = false
        //                        return
        //                    }
        //                    if (filename == "")
        //                        alert('此媒体文件已经播放完毕！')
        //                    else {

        //                        mp.Filename = 'media/' + filename;
        //                        alert("开始播放！");
        //                        mp.play();


        //                    }
        //                }
        //                else {

        //                    document.getElementById('BT' + pcqstno).disabled = false
        //                    return
        //                }

        //            }
        //            else {
        //                if (mp.GetPlayState() != 0) {
        //                    alert('媒体文件正在播放！')
        //                    document.getElementById('BT' + pcqstno).disabled = false
        //                    return
        //                }
        //                filename = Getplaytimes(pcExmClsId, pcStuNo, pcqstno)

        //                if (filename.substring(0, 3) == "错误：") {
        //                    alert("提取媒体文件错误！")
        //                    document.getElementById('BT' + pcqstno).disabled = false
        //                    return
        //                }
        //                if (filename == "")
        //                    alert('此媒体文件已经播放完毕！')
        //                else {
        //                    mp.Source = 'media/' + filename
        //                    mp.DoPlay()
        //                }

        //                document.getElementById('BT' + pcqstno).disabled = false
        //            }
        //        }
        //        function setVolume(num, mpname) {
        //            var mp = document.getElementById(mpname);
        //            mpname = mp.name
        //            if (mpname.substring(0, 2) == "MP") {
        //                tnum = mp.Volume + num;
        //                if (tnum > 0) { tnum = 0; }
        //                if (tnum < -10000) { tnum = -10000; }
        //                mp.Volume = tnum;
        //            }
        //            else {
        //                if (num > 0) {
        //                    Vnum = mp.getVolume() + 10
        //                    if (Vnum > 100)
        //                        mp.SetVolume(100)
        //                    else
        //                        mp.SetVolume(Vnum)
        //                }
        //                else {
        //                    Vnum = mp.getVolume() - 10
        //                    if (Vnum < 0)
        //                        mp.SetVolume(0)
        //                    else
        //                        mp.SetVolume(Vnum)
        //                }
        //            }
        //        }
        //        function setMPFull(pcqstno) {
        //            var mp = document.getElementById('MP' + pcqstno);
        //            mp.DisplaySize = 3;
        //        }
        document.onkeydown = KeyDown
        function KeyDown(e) {
            //alert(window.event.keyCode);
            //屏蔽鼠标右键、Ctrl+n、shift+F10、F5刷新、退格键
            //alert("ASCII代码是："+event.keyCode);
            if ((window.event.altKey) &&
            ((window.event.keyCode == 37) ||     //屏蔽 Alt+ 方向键 ←
             (window.event.keyCode == 39))) {    //屏蔽 Alt+ 方向键 →
                alert("不准你使用ALT+方向键前进或后退网页！");
                event.returnValue = false;
            }
            if ((event.keyCode == 116) ||                   //屏蔽 F5 刷新键
            (event.keyCode == 112) ||                   //屏蔽 F1               
            (event.ctrlKey && event.keyCode == 82)) { //Ctrl + R
                event.keyCode = 0;
                event.returnValue = false;
            }
            if ((event.ctrlKey) && (event.keyCode == 78))     //屏蔽 Ctrl+n
                event.returnValue = false;
            if ((event.shiftKey) && (event.keyCode == 121)) //屏蔽 shift+F10
                event.returnValue = false;
            if (event.keyCode == 121) //屏蔽 F10
                event.returnValue = false;
            if (event.keyCode == 114) //屏蔽 F3
                event.returnValue = false;
            if (window.event.srcElement.tagName == "A" && window.event.shiftKey)
                window.event.returnValue = false;    //屏蔽 shift 加鼠标左键新开一网页
            if ((window.event.altKey) && (window.event.keyCode == 115)) { //屏蔽Alt+F4
                window.showModelessDialog("about:blank", "", "dialogWidth:1px;dialogheight:1px");
                return false;
            }
        }

        function Opencontent(url) {
            NewWindow = window.open(url, '_blank', "toolbar=no,width=550,height=220,directories=no,status=no,scrollbars=no,resize=no,menubar=no,top=200,left=175")
        }
        document.oncontextmenu = mylock1;

        function mylock1() {
            event.returnValue = false;
        }

        function initAd() {
            //            document.getElementById('AdLayer').style.posTop = -10;
            //            var x = document.body.scrollWidth - 380;
            //            document.getElementById('AdLayer').style.left = x;
            //            MoveLayer('AdLayer');

            //            document.getElementById('ending').style.posTop = -20;
            //            var x1 = document.body.scrollWidth - 800;
            //            document.getElementById('ending').style.left = x1;
            //            MoveLayer1('ending');

            document.getElementById('AdLayer1').style.posTop = -30;
            //            var x2 = document.body.scrollWidth - 270;
            document.getElementById('AdLayer1').style.left = 60;
            MoveLayer2('AdLayer1');

            //document.getElementById('AdLayer3').style.posTop = -30;
            //MoveLayer3('AdLayer3');
        }
        //        function MoveLayer(layerName) {
        //            var y = 30; //浮动广告层固定于浏览器的y方向位置
        //            var diff = (document.documentElement.scrollTop + y - document.getElementById('AdLayer').style.posTop) * .40;
        //            var y = document.documentElement.scrollTop + y - diff;
        //            eval("" + layerName + ".style.posTop = y");
        //            window.setTimeout("MoveLayer('AdLayer')", 90); //设置20毫秒后再调用函数MoveLayer()         
        //        }
        //        function MoveLayer1(layerName) {
        //            var y1 = 300; //浮动广告层固定于浏览器的y方向位置
        //            var diff = (document.documentElement.scrollTop + y1 - document.getElementById('ending').style.posTop) * .40;
        //            var y1 = document.documentElement.scrollTop + y1 - diff;
        //            eval("" + layerName + ".style.posTop = y1");
        //            window.setTimeout("MoveLayer1('ending')", 90); //设置20毫秒后再调用函数MoveLayer()         
        //        }
        function MoveLayer2(layerName) {
            var y2 = 145; //浮动广告层固定于浏览器的y方向位置
            var diff = (document.documentElement.scrollTop + y2 - document.getElementById('AdLayer1').style.posTop) * .40;
            var y2 = document.documentElement.scrollTop + y2 - diff;
            eval("" + layerName + ".style.posTop = y2");
            window.setTimeout("MoveLayer2('AdLayer1')", 1); //设置20毫秒后再调用函数MoveLayer()
        }
        //        function MoveLayer3(layerName) {
        //            var y3 = -36; //浮动广告层固定于浏览器的y方向位置
        //            var diff = (document.documentElement.scrollTop + y3 - document.getElementById('AdLayer1').style.posTop) * .40;
        //            var y3 = document.documentElement.scrollTop + y3 - diff;
        //            eval("" + layerName + ".style.posTop = y3");
        //            window.setTimeout("MoveLayer3('AdLayer3')", 90); //设置20毫秒后再调用函数MoveLayer()         
        //        }

        function showMin() {
            if (msg.style.display == "none") {
                msg.style.display = "block";
                document.getElementById('minOrmax').title = "最小化";
                document.getElementById('minOrmax').innerHTML = "0";
            }
            else {
                document.getElementById('minOrmax').innerHTML = "2";
                msg.style.display = "none";
                document.getElementById('minOrmax').title = "还原";
            }
        }
        function showMin1() {
            if (msg1.style.display == "none") {
                msg1.style.display = "block";
                jQuery("[ids='msg1']").show();
                document.getElementById('minOrmax1').title = "最小化";
                document.getElementById('minOrmax1').innerHTML = "1";
            }
            else {
                document.getElementById('minOrmax1').innerHTML = "2";
                msg1.style.display = "none";
                jQuery("[ids='msg1']").hide();
                document.getElementById('minOrmax1').title = "还原";
            }
        }
        function moveStart(event, _sId) {
            var oObj = $(_sId);
            oObj.onmousemove = mousemove;
            oObj.onmouseup = mouseup;
            oObj.setCapture ? oObj.setCapture() : function() { };
            oEvent = window.event ? window.event : event;
            var dragData = { x: oEvent.clientX, y: oEvent.clientY };
            var backData = { x: parseInt(oObj.style.top), y: parseInt(oObj.style.left) };

            function mousemove() { //鼠标移动时的方法
                var oEvent = window.event ? window.event : event;
                var iLeft = oEvent.clientX - dragData["x"] + parseInt(oObj.style.left);
                var iTop = oEvent.clientY - dragData["y"] + parseInt(oObj.style.top);
                oObj.style.left = iLeft;
                oObj.style.top = iTop;
                dragData = { x: oEvent.clientX, y: oEvent.clientY };
            }

            function mouseup() { //放开鼠标键时的方法
                var oEvent = window.event ? window.event : event;
                oObj.onmousemove = null;
                oObj.onmouseup = null;
                if (oEvent.clientX < 1 || oEvent.clientY < 1 || oEvent.clientX > document.body.clientWidth || oEvent.clientY > document.body.clientHeight) {
                    oObj.style.left = backData.y;
                    oObj.style.top = backData.x;
                }
                oObj.releaseCapture ? oObj.releaseCapture() : function() { };
            }
        }

        function setWebFont() {
            var bbstr = document.getElementById("D1").value + "pt";
            jQuery("#papertable").find("td[valign='top']").each(function() {
                jQuery(this).css({ "font-size": bbstr, "line-height": bbstr });
            });

        }

        function biaoji(self) {
            var sid = jQuery(self).attr("id");
            var qnum = $(sid).parentElement.parentElement.id;
            var ids = "#divloadnum" + qnum;

            if (jQuery(self).attr('isselect') == "0") {
                jQuery(self).attr('class', 'biaoji1');
                jQuery(self).attr('isselect', '1');
                if (jQuery(ids).hasClass("questionload1")) {
                    jQuery(ids).removeClass("questionload_bordergreen").addClass("questionload_borderred");
                }
                else {
                    jQuery(ids).removeClass("questionload_border").addClass("questionload_borderred");
                }
            }
            else {
                jQuery(self).attr('class', 'biaoji');
                jQuery(self).attr('isselect', '0');
                if (jQuery(ids).hasClass("questionload1")) {
                    jQuery(ids).removeClass("questionload_borderred").addClass("questionload_bordergreen");
                }
                else {
                    jQuery(ids).removeClass("questionload_borderred").addClass("questionload_border");
                }
            }
        }

        function shoucang(self) {
            var QstNo = jQuery(self).attr("QstNo");
            var stuno = jQuery("#frmStuNo").val();
            var Qstpnt = jQuery(self).attr("Qstpnt");
            jQuery.ajax({
                type: "GET",
                url: "NewStudent/errortest/Ajaxshoucang.aspx",
                dataType: "html",
                data: "stuno=" + stuno + "&QstNo=" + QstNo + "&Qstpnt=" + Qstpnt,
                cache: "false",
                beforeSend: function(XMLHttpRequest) {

                },
                success: function(msg) {
                    if (msg.indexOf("successfull") >= 0) {
                        alert("收藏成功！");
                    }
                    else if (msg != "") {
                        alert(msg);
                    }
                },
                complete: function(XMLHttpRequest, textStatus) {

                },
                error: function() {
                    //错误处理
                    alert("服务器超时，请重试！");

                }
            });
        }

        function jiucuo(self) {
            var QstNo = jQuery(self).attr("QstNo");
            var QstID = jQuery(self).attr("QstID");
            //alert(QstNo);

            jQuery("#hidqstno").val(QstNo);
            jQuery("#hidqstid").val(QstID);

            jQuery("#corcont").val('');
            jQuery(".tankuang_box").show();

        }

        function closetankuang() {
            jQuery(".tankuang_box").hide();
        }

        function savejiucuo() {
            var qstno = jQuery("#hidqstno").val();
            var qstid = jQuery("#hidqstid").val();

            var stuno = jQuery("#frmStuNo").val();

            var cont = jQuery("#corcont").val();

            var data = {
                qstno: qstno,
                qstid: qstid,
                stuno: stuno,
                cont: cont,
                qsttype: "save"

            };

            jQuery.ajax({
                type: "GET",
                url: "Students/Ajax/AjaxQstJC.aspx",
                dataType: "html",
                data: data,
                cache: "false",
                beforeSend: function(XMLHttpRequest) {

                },
                success: function(msg) {
                    if (msg.indexOf("successfull") >= 0) {
                        alert("提交成功！");
                        jQuery(".tankuang_box").hide();
                    }
                    else if (msg != "") {
                        alert(msg);
                    }
                },
                complete: function(XMLHttpRequest, textStatus) {

                },
                error: function() {
                    //错误处理
                    alert("服务器超时，请重试！");

                }
            });

        }

        function inputcorqst(type) {

            var cont = jQuery("#corcont").val();
            cont = cont.replace("错误描述：", "");
            cont = cont.replace("本题内容：", "");
            cont = cont.replace("你的答案：", "");

            cont = cont.replace(/\r\n/g, "")
            cont = cont.replace(/\n/g, "");
            cont = cont.replace(/\s/g, "");

            var qstno = jQuery("#hidqstno").val();
            var qstid = jQuery("#hidqstid").val();

            var data = {
                qstno: qstno,
                qstid: qstid,
                qsttype: "getdata",
                qstty: type

            };

            jQuery.ajax({
                type: "GET",
                url: "Students/Ajax/AjaxQstJC.aspx",
                data: data,
                success: function(msg) {

                    if (type == "html") {
                        jQuery("#corcont").val("错误描述：\r\n\r\n\r\n\r\n本题内容：\r\n" + msg);
                    }
                    else if (type == "anw") {
                        jQuery("#corcont").val("错误描述：\r\n\r\n\r\n\r\n本题内容：\r\n" + msg);
                    }
                    else if (type == "pnt") {
                        jQuery("#corcont").val("错误描述：\r\n\r\n\r\n\r\n你的答案：\r\n");
                    }

                },
                complete: function(XMLHttpRequest, textStatus) {

                },
                error: function() {

                    alert("服务器超时，请重试！");

                }
            });



        }

        function showdtk(self) {
            if (jQuery(self).attr("show") == "1") {
                jQuery(self).css("background", "url(images/hidelogo.png) no-repeat");
                jQuery(self).attr("show", "0");

                jQuery("[ids='msg1']").hide();
            }
            else {
                jQuery(self).css("background", "url(images/showlogo.png) no-repeat");
                jQuery(self).attr("show", "1");
                jQuery("[ids='msg1']").show();
            }
        }
    </script>
    
    <script type="text/javascript">
        function openqst(num) {
        
            jQuery("[idt='dvs']").hide();
            jQuery("[ids='tr_" + num + "']").show();
            document.getElementById("qstnum").value = parseInt(num)-1;
            
        }
        
        function preClick() {
var quesioncount = document.getElementById("quesioncount");

            var quesionnum = quesioncount.value;

            var qstnum = document.getElementById("qstnum").value;
            if (qstnum == 0) {
                alert("已是第一道题！");
            }
            else {
                var qn = parseInt(qstnum) - 1;
                var qnt = parseInt(qn) + 1;
                document.getElementById("qstnum").value = qn;
                
                jQuery("[idt='dvs']").hide();
                jQuery("[ids='tr_" + qnt + "']").show();
                 
            }
        
        }

        function nextClick() {

           var quesioncount = document.getElementById("quesioncount");
 var quesionnum = quesioncount.value;

            var qstnum = document.getElementById("qstnum").value;
            var qn = parseInt(qstnum) + 1;
            var qnt = parseInt(qn) + 1;
            document.getElementById("qstnum").value = qn;
            if (qnt > quesionnum) {
                alert("已是最后一道题！");
            } else {
                jQuery("[idt='dvs']").hide();
                jQuery("[ids='tr_" + qnt + "']").show();
            }
        }
    </script>

    <style type="text/css">
        body
        {
            font-family: 微软雅黑;
        }
        td p
        {
            margin: 0;
            padding: 0;
            display: block;
            padding: 10px 0 10px 0;
        }
        .questionload_borderred
        {
            border: 2px solid #ff0000;
        }
        .questionload_border
        {
            border: 2px solid #e9e9e9;
        }
        .questionload_bordergreen
        {
            border: 2px solid #1ca53c;
        }
        .questionload
        {
            width: 23px;
            height: 23px;
            float: left;
            margin: 4px 4px;
            text-align: center;
            background-color: #e9e9e9;
        }
        .questionload a
        {
            text-decoration: none;
            width: 23px;
            height: 23px;
            color: #646464;
        }
        .questionload a:hover
        {
            text-decoration: underline;
            width: 23px;
            height: 23px;
            color: #646464;
        }
        .questionload1
        {
            width: 23px;
            height: 23px;
            float: left;
            margin: 4px 4px;
            text-align: center;
            background-color: #1ca53c;
        }
        .questionload1 a
        {
            text-decoration: none;
            width: 23px;
            height: 23px;
            color: #FFF;
        }
        .questionload1 a:hover
        {
            text-decoration: underline;
            width: 23px;
            height: 23px;
            color: #FFF;
        }
        .biaoji
        {
            background: url(images/biaozhi.jpg) 0px -10px;
            float: right;
            height: 20px;
            line-height: 20px;
            font-size: 14px;
            color: #4d4d4d;
            padding-left: 30px;
            margin-right: 10px;
            cursor: pointer;
        }
        .biaoji1
        {
            background: url(images/biaozhi.jpg) -83px -10px;
            float: right;
            height: 20px;
            line-height: 20px;
            font-size: 14px;
            color: #FF0000;
            padding-left: 30px;
            margin-right: 10px;
            cursor: pointer;
        }
        .shoucang
        {
            background: url(images/biaozhi.jpg) -240px -10px;
            float: right;
            height: 20px;
            line-height: 20px;
            font-size: 14px;
            color: #4d4d4d;
            padding-left: 30px;
            margin-right: 10px;
            cursor: pointer;
        }
        .shoucang1
        {
            background: url(images/biaozhi.jpg) -159px -10px;
            float: right;
            height: 20px;
            line-height: 20px;
            font-size: 14px;
            color: #FF0000;
            padding-left: 30px;
            margin-right: 10px;
            cursor: pointer;
        }
        .jiucuo
        {
            background: url(images/biaozhi.jpg) -468px -10px no-repeat;
            float: right;
            height: 20px;
            line-height: 20px;
            font-size: 14px;
            color: #4d4d4d;
            padding-left: 30px;
            margin-right: 10px;
            cursor: pointer;
        }
        .jiucuo1
        {
            background: url(images/biaozhi.jpg) -468px -10px no-repeat;
            float: right;
            height: 20px;
            line-height: 20px;
            font-size: 14px;
            color: #4d4d4d;
            padding-left: 30px;
            margin-right: 10px;
            cursor: pointer;
        }
        /* popbox */.popbox
        {
            width: 350px;
            height: 180px;
            background-color: #fff;
            border: 1px #8FA4F5 solid;
            padding: 1px;
            position: fixed;
            display: none;
            z-index: 120;
            top: 300px;
            left: 700px;
        }
        .popbox h2
        {
            height: 25px;
            font-size: 14px;
            background-color: #3366cc;
            position: relative;
            padding-left: 10px;
            line-height: 25px;
            color: #fff;
        }
        .popbox h2 a
        {
            position: absolute;
            right: 5px;
            font-size: 12px;
            color: #fff;
        }
        .popbox .mainlist
        {
            padding: 10px;
        }
        .popbox .mainlist li
        {
            height: 24px;
            line-height: 24px;
        }
        .popbox .mainlist li span
        {
            margin: 0 5px 0 0;
            font-family: "宋体";
            font-size: 12px;
            font-weight: 400;
            color: #ddd;
        }
        #Camera_popbox
        {
            width: 350px;
            height: 110px;
            background-color: #fff;
            border: 1px #8FA4F5 solid;
            padding: 1px;
            position: fixed;
            display: none;
            z-index: 150;
            top: 300px;
            left: 700px;
        }
        #Camera_popbox .Camera_mainlist
        {
            padding: 10px;
        }
        #Camera_popbox .Camera_mainlist li
        {
            height: 24px;
            line-height: 24px;
        }
        #Camera_popbox .Camera_mainlist li span
        {
            margin: 0 5px 0 0;
            font-family: "宋体";
            font-size: 12px;
            font-weight: 400;
            color: #ddd;
        }
        #screen
        {
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
            display: none;
            z-index: 100;
            background-color: #666;
            opacity: 0.5;
            filter: alpha(opacity=50);
            -moz-opacity: 0.5;
        }
        .tabWordBreak
        {
            table-layout: fixed;
            word-wrap: break-word;
            word-break: break-all;
        }
        .style1
        {
            table-layout: fixed;
            word-wrap: break-word;
            word-break: break-all;
            width: 960px;
            padding-left: 20px;
            padding-top: 70px;
        }
    </style>
    <style type="text/css">
        ul, li
        {
            margin: 0;
            padding: 0;
            font-size: 13px;
        }
        ul, li
        {
            list-style: none;
        }
        #divselect
        {
            float: left;
            margin-top: 3px;
            width: 100px;
            z-index: 10000;
        }
        #divselect cite
        {
            width: 68px;
            height: 18px;
            line-height: 18px;
            display: block;
            color: Blue;
            cursor: pointer;
            font-style: normal;
            padding-left: 2px;
            padding-right: 30px;
            border: 1px solid #333333;
            background: url(images/xjt.png) no-repeat right center;
        }
        #divselect ul
        {
            width: 100px;
            border: 1px solid #333333;
            background-color: #ffffff;
            position: absolute;
            z-index: 20000;
            margin-top: -1px;
            display: none;
        }
        #divselect ul li
        {
            height: 18px;
            line-height: 18px;
        }
        #divselect ul li a
        {
            display: block;
            height: 18px;
            color: Blue;
            text-decoration: none;
            padding-left: 10px;
            padding-right: 10px;
        }
        #divselect ul li a:hover
        {
            background-color: #CCC;
        }
    </style>
    <style type="text/css">
        .clearfix:after
        {
            content: '';
            clear: both;
            display: table;
        }
        .clearfix
        {
            zoom: 1;
        }
        .uploder-container
        {
            float: left;
        }
        .x-button
        {
            padding: 10px 25px;
            border-radius: 3px;
            text-align: center;
            text-decoration: none;
            background-color: #0a82e4;
            color: #ffffff;
            font-size: 17px;
            margin: 0;
            white-space: nowrap;
            cursor: pointer;
            min-width: 60px;
            _width: 60px;
        }
        .uploder-container .x-button
        {
            display: inline-block;
            vertical-align: top;
            margin-right: 5px;
        }
        .upload-target
        {
            margin-bottom: 15px;
        }
        .upload-view
        {
            height: auto;
            overflow: hidden;
        }
        .upload-input
        {
            position: absolute;
            overflow: hidden;
        }
        .upload-html4
        {
            position: absolute;
            left: -10000px;
            top: -10000px;
        }
        .u-loaded
        {
            color: green;
        }
        .u-total
        {
            color: #FC5900;
        }
        .u-item
        {
            float: left;
            width: 136px;
            height: 180px;
            margin-right: 10px;
            cursor: pointer;
            position: relative;
        }
        .u-first
        {
            margin: 0;
        }
        .u-img
        {
            width: 136px;
            height: 136px;
            background-color: Gray;
            display: table-cell;
            vertical-align: middle;
            text-align: center; *display:block;*font-size:110px;overflow:hidden;}
        .u-img img
        {
            vertical-align: middle;
        }
        .u-name
        {
            text-align: center;
            height: 24px;
            line-height: 24px;
            overflow: hidden;
        }
        .u-progress-bar, .u-detail
        {
            width: 100%;
            height: 20px;
        }
        .u-progress-bar
        {
            background-color: #7ce4e6;
            opacity: 0.3;
        }
        .u-progress
        {
            width: 0;
            height: 100%;
            background-color: green;
            opacity: 0.5;
        }
        .u-detail
        {
            position: absolute;
            left: 0;
            bottom: 22px;
            line-height: 20px;
            text-align: center;
            color: Blue;
        }
        .u-item .u-moveimg
        {
            position: absolute;
            right: 0px;
            top: 0px;
            width: 18px;
            height: 18px;
            background: url('images/hint3.png') no-repeat;
            cursor: pointer;
        }
    </style>
    <style type="text/css">
        .uploader_pic
        {
            width: 300px;
            height: 300px;
            position: absolute;
            left: 150px;
            top: 50px;
            display: table-cell;
            vertical-align: middle;
            text-align: center; *display:block;*font-size:100px;}
        .uploader_pic img
        {
            vertical-align: middle;
        }
        .uploader_pic span
        {
            position: absolute;
            width: 20px;
            height: 20px;
            left: 450px;
            top: 50px;
            background: #fff;
            text-align: center;
        }
    </style>
    <style type="text/css">
        .tankuang_box
        {
            position: fixed;
            top: 0;
            left: 0;
            z-index: 9999;
            background: rgba(0, 0, 0, 0.5);
            width: 100%;
            height: 100%;
            display: none;
        }
        .tankuang
        {
            width: 650px;
            background: #fff;
            border-radius: 4px;
            position: fixed;
            left: 50%;
            top: 50%;
            z-index: 10000;
            -webkit-transform: translate(-50%, -50%);
            -ms-transform: translate(-50%, -50%);
            -o-transform: translate(-50%, -50%);
            -moz-transform: translate(-50%, -50%);
            transform: translate(-50%, -50%);
            overflow: hidden;
        }
        .tanclose
        {
            background: #fff;
            cursor: pointer;
            float: left;
            border: 2px solid #ededed;
            color: #999;
            width: 70px;
            height: 30px;
            line-height: 30px;
            text-align: center;
        }
        .tansave
        {
            background: #06A774;
            cursor: pointer;
            float: left;
            border: 2px solid #06A774;
            color: #fff;
            width: 70px;
            height: 30px;
            line-height: 30px;
            text-align: center;
            margin-right: 30px;
        }
        .topa
        {
            float: left;
            margin-right: 10px;
            text-decoration: underline;
            cursor: pointer;
        }
        .topa:hover
        {
            text-decoration: none;
        }
        .button
        {
            background: #0a82e4 !important;
        }
    </style>
<link href="App_Themes/public/formTemp.css" type="text/css" rel="stylesheet" /></head>
<body style="margin: 0; background-color: #eee; min-width: 1400px;" onload="Window_OnLoad();">
    <form method="post" action="./PaperAllN.aspx?exmClsId=1136&amp;stu_no=130682200311195435" id="form">
<div class="aspNetHidden">
<input type="hidden" name="__VIEWSTATE" id="__VIEWSTATE" value="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" />
</div>

<div class="aspNetHidden">

	<input type="hidden" name="__VIEWSTATEGENERATOR" id="__VIEWSTATEGENERATOR" value="C505DEB3" />
</div>
    <div id="AdLayer3" style="width: 100%; _width: 1310px; min-width: 1310px; background-color: #c9c9c9;
        font-weight: bold; text-align: center; margin: 0 atuo; height: 50px; line-height: 50px;
        top: 0px; *position: absolute; _position: absolute; position: fixed; clear: both;
        z-index: 3">
        <span id="lblExamName">人工四级-技能-L</span>
    </div>
    <a name="aname1"></a>
    <table border="0" cellpadding="0" cellspacing="0" style="margin: 0 atuo; overflow-x: auto;">
        <tr>
            <td style="width: 350px;" valign="top">
                
                <div id="AdLayer1" style="top: 70px; left: 60px; width: 290px; clear: both; position: fixed;">
                    <div style="width: 250px; line-height: 30px; padding: 10px 20px; background-color: #fff;">
                        <video id="video1" style="width: 250px; height: 200px; display: none;" autoplay></video>
                        [<span id="lblStuNo">130682200311195435</span>]<span id="lblStuName">刘金洋</span><br />
                        组织部门：<span id="lblStuUnit">人工四级23人</span></div>
                    <div style="width: 290px; clear: both; background-color: #fff; margin-top: 20px;
                        padding-bottom: 1px;">
                        <div style="height: 30px; line-height: 30px; padding: 10px 0px; background-color: #f9f9f9;
                            font-size: 12px;">
                            <div style="float: left; width: 60px; height: 30px; padding-left: 20px; font-size: 16px;">
                                答题卡</div>
                            <div style="float: right; width: 30px; height: 30px; background: url(images/showlogo.png) no-repeat;
                                cursor: pointer; padding-right: 10px;" show="1" onclick="showdtk(this)">
                            </div>
                        </div>
                        <div ids="msg1">
                            <div style="padding: 10px 20px; font-size: 13px; height: 50px; line-height: 25px;">
                                <div style="font-size: 13px;">
                                    剩余：<input name="frmLeftMinutes" type="hidden" id="frmLeftMinutes" value="120" />
                                    <input id="jishu" type="hidden" value="0">
                                    <input id="openWindowS" type="hidden" value="0">
                                    <input id="oEl" type="hidden" value="">
                                    <input id="oElId" type="hidden" value="">
                                    <input id="oElDilog" type="hidden" value="">
                                    <input name="lockNum" type="hidden" id="lockNum" value="5" />
                                    <input id="dispRemainM" type="text" readonly="readonly" style="outline: none; width: 27px;
                                        text-align: center; border: 0px; background-color: #e9f5fe; color: Red; font-weight: bold;" />
                                    <font color="#000000">分钟<span id="spanS" style="color: Red; font-weight: bold;">59</span>秒</font>
                                </div>
                                <div style="padding-right: 10px;">
                                    试卷字号：<select id="D1" onchange="setWebFont()">
                                        <option value="9">9pt</option>
                                        <option value="10">10pt</option>
                                        <option value="11">11pt</option>
                                        <option value="12">12pt</option>
                                        <option value="13">13pt</option>
                                        <option value="14">14pt</option>
                                        <option value="15">15pt</option>
                                        <option value="16">16pt</option>
                                        <option value="17">17pt</option>
                                        <option value="18">18pt</option>
                                        <option value="19">19pt</option>
                                        <option value="20">20pt</option>
                                        <option value="21">21pt</option>
                                        <option value="22">22pt</option>
                                        <option value="23">23pt</option>
                                        <option value="24">24pt</option>
                                        <option value="25">25pt</option>
                                        <option value="26">26pt</option>
                                        <option value="27">27pt</option>
                                        <option value="28">28pt</option>
                                        <option value="29">29pt</option>
                                        <option value="30">30pt</option>
                                        <option value="31">31pt</option>
                                    </select>
                                    
                                </div>
                            </div>
                            <div id="msg1" style="clear: both; width: 250px; margin: 0px auto; line-height: 23px;
                                font-size: 12px; color: #646464; overflow: auto;">
                            </div>
                            <div style="clear: both; width: 250px; height: 35px; text-align: right; padding-top: 10px;">
                                <div style="float: right; line-height: 25px; height: 25px; font-size: 13px;">
                                    标记</div>
                                <div class="questionload_borderred" style="height: 16px; width: 16px; float: right;
                                    margin: 4px 4px;">
                                    &nbsp;</div>
                                <div style="float: right; line-height: 25px; height: 25px; font-size: 13px; padding-right: 10px;">
                                    已答</div>
                                <div class="questionload1" style="height: 18px; width: 18px; float: right">
                                    &nbsp;</div>
                                <div style="float: right; line-height: 25px; height: 25px; padding-right: 10px; font-size: 13px;">
                                    未答</div>
                                <div class="questionload" style="height: 18px; width: 18px; float: right">
                                    &nbsp;</div>
                            </div>
                            <div id="uppaper" style="clear: both; width: 250px; margin: 10px auto 20px auto;
                                line-height: 35px; height: 35px; background-color: #1ca53c; text-align: center;
                                cursor: pointer;" onclick="UplaodPaper()">
                                <div id="uppaper" style="font-size: 16px; width: 250px; color: #ffffff">
                                    我要交卷</div>
                            </div>
                            
                        </div>
                    </div>
                </div>
            </td>
            <td class="style1">
                
                
                <table id="papertable" width="100%" border="0" align="center" cellspacing="0" cellpadding="0"
                    style="line-height: 200%; background-color: #fff; border-collapse: collapse">
                    <tr>
                        <td align="left" class="black_12px_lh16px" style="padding: 10px; font-family: 微软雅黑;">
                            
                            <table width="100%" style="word-break: break-all;" id="tablediv"><tr><td><h3 style="CURSOR: hand" id="Segment1" onclick="DispSegment(1,0)">
<table width="100%" border="0" cellpadding="0" cellspacing="0" bgcolor="#e9f5fe">
<tr>
<td width="2%" height="24">
<div align="right">
<span style="CURSOR: hand">
<IMG  src="/images/minus.gif" width="16" height="16" border="0" id="node1" valign="middle">&nbsp;
</span>
</div>
</td>
<td width="98%" class="Segment">问答题：本题共5题，每题20分。</td>
</tr>
</table>
</td></tr><tr id="trLX0001" ids="tr_1" idt="dvs">
<td id="1"> 
<div name="DIVLX0001" id="DIVLX0001"><table><tr><td valign="top"><p><b>1、</b></p></td><td valign="top" style="width:870px;"><p>1.人工智能的定义及分类。<br/>2.人工智能与计算机程序相比对，优点有那些？</p><p>&nbsp;&nbsp;&nbsp;<textarea rows="8" onchange="divclicks(7,event)" name="TLX0001" cols="80"></textarea></p></p><p><input type="button" value="保存" class="button"  onclick="saveanws(7,event)" name="TLX00011"  id="TLX0001" /></p><div class="uploder-container" ids="LX0001"> <div style="height:auto;overflow:hidden;"><div  style="float:left;"><a ids="LX0001" class="x-button" onclick="phone_uplod(this)">手机扫描上传图片</a></div></div> <div id="upload-image-view-LX0001" class="upload-view clearfix"> </div> </div></td></tr></table></div><div><a name="aname2"></a></div><div name="caozuodiv" style="height: 20px; padding: 0px 10px 0px 10px; text-align:right"><div id="shoucang1" isselect="0" names="shoucang" name="shoucang" QstNo="LX0001" Qstpnt="20.000"  class="shoucang" onmouseover="jQuery(this).attr('class','shoucang1')" onmouseleave="if(jQuery(this).attr('isselect')=='0'){jQuery(this).attr('class','shoucang')}" onclick="shoucang(this);">收藏</div><div id="jiucuo1" isselect="0"  names="jiucuo"  name="jiucuo" class="jiucuo" QstNo="LX0001" onmouseover="jQuery(this).attr('class','jiucuo1')" onmouseleave="if(jQuery(this).attr('isselect')=='0'){jQuery(this).attr('class','jiucuo')}" onclick="jiucuo(this);" style="display:none">纠错</div><div id="jiucuo1" isselect="0"  names="jiucuo"  name="jiucuo" class="jiucuo" QstID="25977" QstNo="LX0001" onmouseover="jQuery(this).attr('class','jiucuo1')" onmouseleave="if(jQuery(this).attr('isselect')=='0'){jQuery(this).attr('class','jiucuo')}" onclick="jiucuo(this);" >纠错</div><div id="biaoji1" isselect="0" name="biaoji"  names="biaoji" QstNo="LX0001" class="biaoji" onmouseover="jQuery(this).attr('class','biaoji1')" onmouseleave="if(jQuery(this).attr('isselect')=='0'){jQuery(this).attr('class','biaoji')}" onclick="biaoji(this);">标记</div></div><hr size="1" color="#CCCCCC">
</td>
</tr>
<tr id="trLX0011" ids="tr_2" idt="dvs">
<td id="2"> 
<div name="DIVLX0011" id="DIVLX0011"><table><tr><td valign="top"><p><b>2、</b></p></td><td valign="top" style="width:870px;"><p>语料标准化是将客户的原始语料相似问题转化成标准问题的一个过程，为了使在线机器人更高效、准确的识别问题，需要将已经清洗处理的客户原始语料转化为标准语料。假如你是某电商平台智能客服系统的语料整理员，需要在以下客户原始语料中找出与标准语料“如何申请退款？”对应的语料（退款指将已支付款项退回原支付账户）。
<br/>示例：0、怎样申请退货？&nbsp;&nbsp;1、怎么办理退款手续？
<br/>正确答案为1
<br/>解析：
<br/>“怎么办理退款手续”与标准语料同义；“怎样申请退货”提问与“如何申请退款”无关。
<br/>待识别语料：1、我该如何申请退款？2、怎么发起退货流程？3、申请退款的步骤是什么？4、怎样把钱退回来？5、我想知道怎么申请售后退款？6、在哪里提交退款申请？7、如何取消订单？8、我要怎么才能拿到退款？9、申请退款的入口在哪里？10、怎样完成退货？</p><p>&nbsp;&nbsp;&nbsp;<textarea rows="8" onchange="divclicks(7,event)" name="TLX0011" cols="80"></textarea></p></p><p><input type="button" value="保存" class="button"  onclick="saveanws(7,event)" name="TLX00111"  id="TLX0011" /></p><div class="uploder-container" ids="LX0011"> <div style="height:auto;overflow:hidden;"><div  style="float:left;"><a ids="LX0011" class="x-button" onclick="phone_uplod(this)">手机扫描上传图片</a></div></div> <div id="upload-image-view-LX0011" class="upload-view clearfix"> </div> </div></td></tr></table></div><div><a name="aname3"></a></div><div name="caozuodiv" style="height: 20px; padding: 0px 10px 0px 10px; text-align:right"><div id="shoucang2" isselect="0" names="shoucang" name="shoucang" QstNo="LX0011" Qstpnt="20.000"  class="shoucang" onmouseover="jQuery(this).attr('class','shoucang1')" onmouseleave="if(jQuery(this).attr('isselect')=='0'){jQuery(this).attr('class','shoucang')}" onclick="shoucang(this);">收藏</div><div id="jiucuo2" isselect="0"  names="jiucuo"  name="jiucuo" class="jiucuo" QstNo="LX0011" onmouseover="jQuery(this).attr('class','jiucuo1')" onmouseleave="if(jQuery(this).attr('isselect')=='0'){jQuery(this).attr('class','jiucuo')}" onclick="jiucuo(this);" style="display:none">纠错</div><div id="jiucuo2" isselect="0"  names="jiucuo"  name="jiucuo" class="jiucuo" QstID="25987" QstNo="LX0011" onmouseover="jQuery(this).attr('class','jiucuo1')" onmouseleave="if(jQuery(this).attr('isselect')=='0'){jQuery(this).attr('class','jiucuo')}" onclick="jiucuo(this);" >纠错</div><div id="biaoji2" isselect="0" name="biaoji"  names="biaoji" QstNo="LX0011" class="biaoji" onmouseover="jQuery(this).attr('class','biaoji1')" onmouseleave="if(jQuery(this).attr('isselect')=='0'){jQuery(this).attr('class','biaoji')}" onclick="biaoji(this);">标记</div></div><hr size="1" color="#CCCCCC">
</td>
</tr>
<tr id="trLX0004" ids="tr_3" idt="dvs">
<td id="3"> 
<div name="DIVLX0004" id="DIVLX0004"><table><tr><td valign="top"><p><b>3、</b></p></td><td valign="top" style="width:870px;"><p>在数据预处理环节，将数据转化为零均值、单位方差分布的标准化操作，与把数据映射至固定区间（如&nbsp;[0,1]）的归一化处理，二者存在哪些本质差异？</p><p>&nbsp;&nbsp;&nbsp;<textarea rows="8" onchange="divclicks(7,event)" name="TLX0004" cols="80"></textarea></p></p><p><input type="button" value="保存" class="button"  onclick="saveanws(7,event)" name="TLX00041"  id="TLX0004" /></p><div class="uploder-container" ids="LX0004"> <div style="height:auto;overflow:hidden;"><div  style="float:left;"><a ids="LX0004" class="x-button" onclick="phone_uplod(this)">手机扫描上传图片</a></div></div> <div id="upload-image-view-LX0004" class="upload-view clearfix"> </div> </div></td></tr></table></div><div><a name="aname4"></a></div><div name="caozuodiv" style="height: 20px; padding: 0px 10px 0px 10px; text-align:right"><div id="shoucang3" isselect="0" names="shoucang" name="shoucang" QstNo="LX0004" Qstpnt="20.000"  class="shoucang" onmouseover="jQuery(this).attr('class','shoucang1')" onmouseleave="if(jQuery(this).attr('isselect')=='0'){jQuery(this).attr('class','shoucang')}" onclick="shoucang(this);">收藏</div><div id="jiucuo3" isselect="0"  names="jiucuo"  name="jiucuo" class="jiucuo" QstNo="LX0004" onmouseover="jQuery(this).attr('class','jiucuo1')" onmouseleave="if(jQuery(this).attr('isselect')=='0'){jQuery(this).attr('class','jiucuo')}" onclick="jiucuo(this);" style="display:none">纠错</div><div id="jiucuo3" isselect="0"  names="jiucuo"  name="jiucuo" class="jiucuo" QstID="25980" QstNo="LX0004" onmouseover="jQuery(this).attr('class','jiucuo1')" onmouseleave="if(jQuery(this).attr('isselect')=='0'){jQuery(this).attr('class','jiucuo')}" onclick="jiucuo(this);" >纠错</div><div id="biaoji3" isselect="0" name="biaoji"  names="biaoji" QstNo="LX0004" class="biaoji" onmouseover="jQuery(this).attr('class','biaoji1')" onmouseleave="if(jQuery(this).attr('isselect')=='0'){jQuery(this).attr('class','biaoji')}" onclick="biaoji(this);">标记</div></div><hr size="1" color="#CCCCCC">
</td>
</tr>
<tr id="trLX0007" ids="tr_4" idt="dvs">
<td id="4"> 
<div name="DIVLX0007" id="DIVLX0007"><table><tr><td valign="top"><p><b>4、</b></p></td><td valign="top" style="width:870px;"><p>一.当今科技发展浪潮中，人工智能技术在不同行业和生活场景里发挥着重要作用，请列举五个典型的应用领域，并分别给出具体的应用实例。<br/>二.相较于传统技术，人工智能在实际应用中展现出独特的优势与特性。请概括人工智能应用所具备的五个显著特点，并对每个特点进行简要说明。</p><p>&nbsp;&nbsp;&nbsp;<textarea rows="8" onchange="divclicks(7,event)" name="TLX0007" cols="80"></textarea></p></p><p><input type="button" value="保存" class="button"  onclick="saveanws(7,event)" name="TLX00071"  id="TLX0007" /></p><div class="uploder-container" ids="LX0007"> <div style="height:auto;overflow:hidden;"><div  style="float:left;"><a ids="LX0007" class="x-button" onclick="phone_uplod(this)">手机扫描上传图片</a></div></div> <div id="upload-image-view-LX0007" class="upload-view clearfix"> </div> </div></td></tr></table></div><div><a name="aname5"></a></div><div name="caozuodiv" style="height: 20px; padding: 0px 10px 0px 10px; text-align:right"><div id="shoucang4" isselect="0" names="shoucang" name="shoucang" QstNo="LX0007" Qstpnt="20.000"  class="shoucang" onmouseover="jQuery(this).attr('class','shoucang1')" onmouseleave="if(jQuery(this).attr('isselect')=='0'){jQuery(this).attr('class','shoucang')}" onclick="shoucang(this);">收藏</div><div id="jiucuo4" isselect="0"  names="jiucuo"  name="jiucuo" class="jiucuo" QstNo="LX0007" onmouseover="jQuery(this).attr('class','jiucuo1')" onmouseleave="if(jQuery(this).attr('isselect')=='0'){jQuery(this).attr('class','jiucuo')}" onclick="jiucuo(this);" style="display:none">纠错</div><div id="jiucuo4" isselect="0"  names="jiucuo"  name="jiucuo" class="jiucuo" QstID="25983" QstNo="LX0007" onmouseover="jQuery(this).attr('class','jiucuo1')" onmouseleave="if(jQuery(this).attr('isselect')=='0'){jQuery(this).attr('class','jiucuo')}" onclick="jiucuo(this);" >纠错</div><div id="biaoji4" isselect="0" name="biaoji"  names="biaoji" QstNo="LX0007" class="biaoji" onmouseover="jQuery(this).attr('class','biaoji1')" onmouseleave="if(jQuery(this).attr('isselect')=='0'){jQuery(this).attr('class','biaoji')}" onclick="biaoji(this);">标记</div></div><hr size="1" color="#CCCCCC">
</td>
</tr>
<tr id="trLX0008" ids="tr_5" idt="dvs">
<td id="5"> 
<div name="DIVLX0008" id="DIVLX0008"><table><tr><td valign="top"><p><b>5、</b></p></td><td valign="top" style="width:870px;"><p>当图像数据出现部分遮挡、画面模糊的情况时，怎样合理开展标注工作以确保标注结果的准确性？</p><p>&nbsp;&nbsp;&nbsp;<textarea rows="8" onchange="divclicks(7,event)" name="TLX0008" cols="80"></textarea></p></p><p><input type="button" value="保存" class="button"  onclick="saveanws(7,event)" name="TLX00081"  id="TLX0008" /></p><div class="uploder-container" ids="LX0008"> <div style="height:auto;overflow:hidden;"><div  style="float:left;"><a ids="LX0008" class="x-button" onclick="phone_uplod(this)">手机扫描上传图片</a></div></div> <div id="upload-image-view-LX0008" class="upload-view clearfix"> </div> </div></td></tr></table></div><div><a name="aname6"></a></div><div name="caozuodiv" style="height: 20px; padding: 0px 10px 0px 10px; text-align:right"><div id="shoucang5" isselect="0" names="shoucang" name="shoucang" QstNo="LX0008" Qstpnt="20.000"  class="shoucang" onmouseover="jQuery(this).attr('class','shoucang1')" onmouseleave="if(jQuery(this).attr('isselect')=='0'){jQuery(this).attr('class','shoucang')}" onclick="shoucang(this);">收藏</div><div id="jiucuo5" isselect="0"  names="jiucuo"  name="jiucuo" class="jiucuo" QstNo="LX0008" onmouseover="jQuery(this).attr('class','jiucuo1')" onmouseleave="if(jQuery(this).attr('isselect')=='0'){jQuery(this).attr('class','jiucuo')}" onclick="jiucuo(this);" style="display:none">纠错</div><div id="jiucuo5" isselect="0"  names="jiucuo"  name="jiucuo" class="jiucuo" QstID="25984" QstNo="LX0008" onmouseover="jQuery(this).attr('class','jiucuo1')" onmouseleave="if(jQuery(this).attr('isselect')=='0'){jQuery(this).attr('class','jiucuo')}" onclick="jiucuo(this);" >纠错</div><div id="biaoji5" isselect="0" name="biaoji"  names="biaoji" QstNo="LX0008" class="biaoji" onmouseover="jQuery(this).attr('class','biaoji1')" onmouseleave="if(jQuery(this).attr('isselect')=='0'){jQuery(this).attr('class','biaoji')}" onclick="biaoji(this);">标记</div></div><hr size="1" color="#CCCCCC">
</td>
</tr>
<tr id="trLX0005" ids="tr_6" idt="dvs">
<td id="6"> 
<div name="DIVLX0005" id="DIVLX0005"><table><tr><td valign="top"><p><b>6、</b></p></td><td valign="top" style="width:870px;"><p>为了让机器更精准地&nbsp;“听懂”&nbsp;人类语言，在语音识别技术的实际应用与优化过程中，可通过哪些途径来提升其识别的精确程度？</p><p>&nbsp;&nbsp;&nbsp;<textarea rows="8" onchange="divclicks(7,event)" name="TLX0005" cols="80"></textarea></p></p><p><input type="button" value="保存" class="button"  onclick="saveanws(7,event)" name="TLX00051"  id="TLX0005" /></p><div class="uploder-container" ids="LX0005"> <div style="height:auto;overflow:hidden;"><div  style="float:left;"><a ids="LX0005" class="x-button" onclick="phone_uplod(this)">手机扫描上传图片</a></div></div> <div id="upload-image-view-LX0005" class="upload-view clearfix"> </div> </div></td></tr></table></div><div><a name="aname7"></a></div><div name="caozuodiv" style="height: 20px; padding: 0px 10px 0px 10px; text-align:right"><div id="shoucang6" isselect="0" names="shoucang" name="shoucang" QstNo="LX0005" Qstpnt="20.000"  class="shoucang" onmouseover="jQuery(this).attr('class','shoucang1')" onmouseleave="if(jQuery(this).attr('isselect')=='0'){jQuery(this).attr('class','shoucang')}" onclick="shoucang(this);">收藏</div><div id="jiucuo6" isselect="0"  names="jiucuo"  name="jiucuo" class="jiucuo" QstNo="LX0005" onmouseover="jQuery(this).attr('class','jiucuo1')" onmouseleave="if(jQuery(this).attr('isselect')=='0'){jQuery(this).attr('class','jiucuo')}" onclick="jiucuo(this);" style="display:none">纠错</div><div id="jiucuo6" isselect="0"  names="jiucuo"  name="jiucuo" class="jiucuo" QstID="25981" QstNo="LX0005" onmouseover="jQuery(this).attr('class','jiucuo1')" onmouseleave="if(jQuery(this).attr('isselect')=='0'){jQuery(this).attr('class','jiucuo')}" onclick="jiucuo(this);" >纠错</div><div id="biaoji6" isselect="0" name="biaoji"  names="biaoji" QstNo="LX0005" class="biaoji" onmouseover="jQuery(this).attr('class','biaoji1')" onmouseleave="if(jQuery(this).attr('isselect')=='0'){jQuery(this).attr('class','biaoji')}" onclick="biaoji(this);">标记</div></div><hr size="1" color="#CCCCCC">
</td>
</tr>
<tr id="trLX0006" ids="tr_7" idt="dvs">
<td id="7"> 
<div name="DIVLX0006" id="DIVLX0006"><table><tr><td valign="top"><p><b>7、</b></p></td><td valign="top" style="width:870px;"><p>有一种模型通过让两个组件展开&nbsp;“猫鼠游戏”&nbsp;来提升数据生成质量，解释该模型的工作原理以及这两个组件的具体职责。</p><p>&nbsp;&nbsp;&nbsp;<textarea rows="8" onchange="divclicks(7,event)" name="TLX0006" cols="80"></textarea></p></p><p><input type="button" value="保存" class="button"  onclick="saveanws(7,event)" name="TLX00061"  id="TLX0006" /></p><div class="uploder-container" ids="LX0006"> <div style="height:auto;overflow:hidden;"><div  style="float:left;"><a ids="LX0006" class="x-button" onclick="phone_uplod(this)">手机扫描上传图片</a></div></div> <div id="upload-image-view-LX0006" class="upload-view clearfix"> </div> </div></td></tr></table></div><div><a name="aname8"></a></div><div name="caozuodiv" style="height: 20px; padding: 0px 10px 0px 10px; text-align:right"><div id="shoucang7" isselect="0" names="shoucang" name="shoucang" QstNo="LX0006" Qstpnt="20.000"  class="shoucang" onmouseover="jQuery(this).attr('class','shoucang1')" onmouseleave="if(jQuery(this).attr('isselect')=='0'){jQuery(this).attr('class','shoucang')}" onclick="shoucang(this);">收藏</div><div id="jiucuo7" isselect="0"  names="jiucuo"  name="jiucuo" class="jiucuo" QstNo="LX0006" onmouseover="jQuery(this).attr('class','jiucuo1')" onmouseleave="if(jQuery(this).attr('isselect')=='0'){jQuery(this).attr('class','jiucuo')}" onclick="jiucuo(this);" style="display:none">纠错</div><div id="jiucuo7" isselect="0"  names="jiucuo"  name="jiucuo" class="jiucuo" QstID="25982" QstNo="LX0006" onmouseover="jQuery(this).attr('class','jiucuo1')" onmouseleave="if(jQuery(this).attr('isselect')=='0'){jQuery(this).attr('class','jiucuo')}" onclick="jiucuo(this);" >纠错</div><div id="biaoji7" isselect="0" name="biaoji"  names="biaoji" QstNo="LX0006" class="biaoji" onmouseover="jQuery(this).attr('class','biaoji1')" onmouseleave="if(jQuery(this).attr('isselect')=='0'){jQuery(this).attr('class','biaoji')}" onclick="biaoji(this);">标记</div></div><hr size="1" color="#CCCCCC">
</td>
</tr>
<tr id="trLX0003" ids="tr_8" idt="dvs">
<td id="8"> 
<div name="DIVLX0003" id="DIVLX0003"><table><tr><td valign="top"><p><b>8、</b></p></td><td valign="top" style="width:870px;"><p>在将训练好的模型投入实际应用时，需要评估哪些技术要素以确保系统高效稳定运行？”</p><p>&nbsp;&nbsp;&nbsp;<textarea rows="8" onchange="divclicks(7,event)" name="TLX0003" cols="80"></textarea></p></p><p><input type="button" value="保存" class="button"  onclick="saveanws(7,event)" name="TLX00031"  id="TLX0003" /></p><div class="uploder-container" ids="LX0003"> <div style="height:auto;overflow:hidden;"><div  style="float:left;"><a ids="LX0003" class="x-button" onclick="phone_uplod(this)">手机扫描上传图片</a></div></div> <div id="upload-image-view-LX0003" class="upload-view clearfix"> </div> </div></td></tr></table></div><div><a name="aname9"></a></div><div name="caozuodiv" style="height: 20px; padding: 0px 10px 0px 10px; text-align:right"><div id="shoucang8" isselect="0" names="shoucang" name="shoucang" QstNo="LX0003" Qstpnt="20.000"  class="shoucang" onmouseover="jQuery(this).attr('class','shoucang1')" onmouseleave="if(jQuery(this).attr('isselect')=='0'){jQuery(this).attr('class','shoucang')}" onclick="shoucang(this);">收藏</div><div id="jiucuo8" isselect="0"  names="jiucuo"  name="jiucuo" class="jiucuo" QstNo="LX0003" onmouseover="jQuery(this).attr('class','jiucuo1')" onmouseleave="if(jQuery(this).attr('isselect')=='0'){jQuery(this).attr('class','jiucuo')}" onclick="jiucuo(this);" style="display:none">纠错</div><div id="jiucuo8" isselect="0"  names="jiucuo"  name="jiucuo" class="jiucuo" QstID="25979" QstNo="LX0003" onmouseover="jQuery(this).attr('class','jiucuo1')" onmouseleave="if(jQuery(this).attr('isselect')=='0'){jQuery(this).attr('class','jiucuo')}" onclick="jiucuo(this);" >纠错</div><div id="biaoji8" isselect="0" name="biaoji"  names="biaoji" QstNo="LX0003" class="biaoji" onmouseover="jQuery(this).attr('class','biaoji1')" onmouseleave="if(jQuery(this).attr('isselect')=='0'){jQuery(this).attr('class','biaoji')}" onclick="biaoji(this);">标记</div></div><hr size="1" color="#CCCCCC">
</td>
</tr>
<tr id="trLX0002" ids="tr_9" idt="dvs">
<td id="9"> 
<div name="DIVLX0002" id="DIVLX0002"><table><tr><td valign="top"><p><b>9、</b></p></td><td valign="top" style="width:870px;"><p>“命名实体识别在自然语言处理中的主要目标是什么？它对其他NLP任务有何重要性？”​</p><p>&nbsp;&nbsp;&nbsp;<textarea rows="8" onchange="divclicks(7,event)" name="TLX0002" cols="80"></textarea></p></p><p><input type="button" value="保存" class="button"  onclick="saveanws(7,event)" name="TLX00021"  id="TLX0002" /></p><div class="uploder-container" ids="LX0002"> <div style="height:auto;overflow:hidden;"><div  style="float:left;"><a ids="LX0002" class="x-button" onclick="phone_uplod(this)">手机扫描上传图片</a></div></div> <div id="upload-image-view-LX0002" class="upload-view clearfix"> </div> </div></td></tr></table></div><div><a name="aname10"></a></div><div name="caozuodiv" style="height: 20px; padding: 0px 10px 0px 10px; text-align:right"><div id="shoucang9" isselect="0" names="shoucang" name="shoucang" QstNo="LX0002" Qstpnt="20.000"  class="shoucang" onmouseover="jQuery(this).attr('class','shoucang1')" onmouseleave="if(jQuery(this).attr('isselect')=='0'){jQuery(this).attr('class','shoucang')}" onclick="shoucang(this);">收藏</div><div id="jiucuo9" isselect="0"  names="jiucuo"  name="jiucuo" class="jiucuo" QstNo="LX0002" onmouseover="jQuery(this).attr('class','jiucuo1')" onmouseleave="if(jQuery(this).attr('isselect')=='0'){jQuery(this).attr('class','jiucuo')}" onclick="jiucuo(this);" style="display:none">纠错</div><div id="jiucuo9" isselect="0"  names="jiucuo"  name="jiucuo" class="jiucuo" QstID="25978" QstNo="LX0002" onmouseover="jQuery(this).attr('class','jiucuo1')" onmouseleave="if(jQuery(this).attr('isselect')=='0'){jQuery(this).attr('class','jiucuo')}" onclick="jiucuo(this);" >纠错</div><div id="biaoji9" isselect="0" name="biaoji"  names="biaoji" QstNo="LX0002" class="biaoji" onmouseover="jQuery(this).attr('class','biaoji1')" onmouseleave="if(jQuery(this).attr('isselect')=='0'){jQuery(this).attr('class','biaoji')}" onclick="biaoji(this);">标记</div></div><hr size="1" color="#CCCCCC">
</td>
</tr>
<tr id="trLX0010" ids="tr_10" idt="dvs">
<td id="10"> 
<div name="DIVLX0010" id="DIVLX0010"><table><tr><td valign="top"><p><b>10、</b></p></td><td valign="top" style="width:870px;"><p>一.在人工智能技术范畴内，有一种技术能够让计算机像人类一样&nbsp;“认识”&nbsp;图像，它借助计算机视觉算法，对图像中的物体、场景等进行自动分辨归类，还能运用机器学习和深度学习模型，从图像和视频里提取并分析各类信息，在身份验证、交通管理、文档处理等方面发挥着重要作用。请问这项技术是什么？请阐述其定义 (10分）<br/>二.在深度学习兴起之前，科研人员依靠一系列既定流程实现图像识别任务，这些流程环环相扣，共同完成对图像的识别与分类工作。请系统地梳理传统图像识别的具体流程</p><p>&nbsp;&nbsp;&nbsp;<textarea rows="8" onchange="divclicks(7,event)" name="TLX0010" cols="80"></textarea></p></p><p><input type="button" value="保存" class="button"  onclick="saveanws(7,event)" name="TLX00101"  id="TLX0010" /></p><div class="uploder-container" ids="LX0010"> <div style="height:auto;overflow:hidden;"><div  style="float:left;"><a ids="LX0010" class="x-button" onclick="phone_uplod(this)">手机扫描上传图片</a></div></div> <div id="upload-image-view-LX0010" class="upload-view clearfix"> </div> </div></td></tr></table></div><div><a name="aname11"></a></div><div name="caozuodiv" style="height: 20px; padding: 0px 10px 0px 10px; text-align:right"><div id="shoucang10" isselect="0" names="shoucang" name="shoucang" QstNo="LX0010" Qstpnt="20.000"  class="shoucang" onmouseover="jQuery(this).attr('class','shoucang1')" onmouseleave="if(jQuery(this).attr('isselect')=='0'){jQuery(this).attr('class','shoucang')}" onclick="shoucang(this);">收藏</div><div id="jiucuo10" isselect="0"  names="jiucuo"  name="jiucuo" class="jiucuo" QstNo="LX0010" onmouseover="jQuery(this).attr('class','jiucuo1')" onmouseleave="if(jQuery(this).attr('isselect')=='0'){jQuery(this).attr('class','jiucuo')}" onclick="jiucuo(this);" style="display:none">纠错</div><div id="jiucuo10" isselect="0"  names="jiucuo"  name="jiucuo" class="jiucuo" QstID="25986" QstNo="LX0010" onmouseover="jQuery(this).attr('class','jiucuo1')" onmouseleave="if(jQuery(this).attr('isselect')=='0'){jQuery(this).attr('class','jiucuo')}" onclick="jiucuo(this);" >纠错</div><div id="biaoji10" isselect="0" name="biaoji"  names="biaoji" QstNo="LX0010" class="biaoji" onmouseover="jQuery(this).attr('class','biaoji1')" onmouseleave="if(jQuery(this).attr('isselect')=='0'){jQuery(this).attr('class','biaoji')}" onclick="biaoji(this);">标记</div></div><hr size="1" color="#CCCCCC">
</td>
</tr>
<tr id="trLX0009" ids="tr_11" idt="dvs">
<td id="11"> 
<div name="DIVLX0009" id="DIVLX0009"><table><tr><td valign="top"><p><b>11、</b></p></td><td valign="top" style="width:870px;"><p>一.从计算机视觉和人工智能角度出发，能够实现车辆关键信息自动提取，并广泛应用于停车管理等场景的技术，如何定义？其涵盖哪些常见功能？<br/>二.当利用技术手段对车辆进行识别时，从原始图像到形成有效识别数据，需要经过怎样的处理过程？请按步骤进行说明&nbsp;。</p><p>&nbsp;&nbsp;&nbsp;<textarea rows="8" onchange="divclicks(7,event)" name="TLX0009" cols="80"></textarea></p></p><p><input type="button" value="保存" class="button"  onclick="saveanws(7,event)" name="TLX00091"  id="TLX0009" /></p><div class="uploder-container" ids="LX0009"> <div style="height:auto;overflow:hidden;"><div  style="float:left;"><a ids="LX0009" class="x-button" onclick="phone_uplod(this)">手机扫描上传图片</a></div></div> <div id="upload-image-view-LX0009" class="upload-view clearfix"> </div> </div></td></tr></table></div><div><a name="aname12"></a></div><div name="caozuodiv" style="height: 20px; padding: 0px 10px 0px 10px; text-align:right"><div id="shoucang11" isselect="0" names="shoucang" name="shoucang" QstNo="LX0009" Qstpnt="20.000"  class="shoucang" onmouseover="jQuery(this).attr('class','shoucang1')" onmouseleave="if(jQuery(this).attr('isselect')=='0'){jQuery(this).attr('class','shoucang')}" onclick="shoucang(this);">收藏</div><div id="jiucuo11" isselect="0"  names="jiucuo"  name="jiucuo" class="jiucuo" QstNo="LX0009" onmouseover="jQuery(this).attr('class','jiucuo1')" onmouseleave="if(jQuery(this).attr('isselect')=='0'){jQuery(this).attr('class','jiucuo')}" onclick="jiucuo(this);" style="display:none">纠错</div><div id="jiucuo11" isselect="0"  names="jiucuo"  name="jiucuo" class="jiucuo" QstID="25985" QstNo="LX0009" onmouseover="jQuery(this).attr('class','jiucuo1')" onmouseleave="if(jQuery(this).attr('isselect')=='0'){jQuery(this).attr('class','jiucuo')}" onclick="jiucuo(this);" >纠错</div><div id="biaoji11" isselect="0" name="biaoji"  names="biaoji" QstNo="LX0009" class="biaoji" onmouseover="jQuery(this).attr('class','biaoji1')" onmouseleave="if(jQuery(this).attr('isselect')=='0'){jQuery(this).attr('class','biaoji')}" onclick="biaoji(this);">标记</div></div><hr size="1" color="#CCCCCC">
</td>
</tr>
</table>
<input type="hidden" id="CztNum" name="CztNum" size="10" value="0"><input type="hidden" id="ExmNO" name="ExmNO" size="10" value="SJ000132"><input type="hidden" id="WS_DIR2" name="WS_DIR2" size="3"  value="c:\exam"><input type="hidden" id="SEGMENT" name="SEGMENT" size="10" value="1"><input type="hidden" id="QINDEX" name="QINDEX" size="90" value="LX0001,7,1,LX0011,7,1,LX0004,7,1,LX0007,7,1,LX0008,7,1,LX0005,7,1,LX0006,7,1,LX0003,7,1,LX0002,7,1,LX0010,7,1,LX0009,7,1,"><input type="hidden" id="frmTestDir" name="frmTestDir" value="c:\exam"><input type="hidden" id="frmTestSysDir" name="frmTestSysDir" value="c:\examsys"><input type="hidden" id="frmTestSysBakDir" name="frmTestSysBakDir" value="c:\examsys.bak"><input type="hidden" id="quesioncount" name="quesioncount" value="11"><input type="hidden" id="loadgroupnum" name="loadgroupnum" value="1"><input type="hidden" id="loadgroupname" name="loadgroupname" value="问答题"><input type="hidden" id="StuNo" name="StuNo"  size="10" value="130682200311195435">

                        </td>
                    </tr>
                </table>
                <div style="width: 100%; margin: 20px auto; text-align: center; height: 100px;">
                    
                    <input type="button" style="border-style: none; width: 250px; margin: 10px auto 20px auto;
                        font-size: 16px; color: #ffffff; line-height: 35px; height: 35px; background-color: #1ca53c;
                        text-align: center; font-family: 微软雅黑; cursor: pointer;" id="presubmitb" value="上一题"
                        name="submitb" onclick="preClick()" />
                        
                    <input type="button"   style="border-style: none; width: 250px; margin: 10px auto 20px auto;
                        font-size: 16px; color: #ffffff; line-height: 35px; height: 35px; background-color: #1ca53c;
                        text-align: center; font-family: 微软雅黑; cursor: pointer;" id="nextsubmitb" value="下一题"
                        name="submitb" onclick="nextClick()" />
                        
                    <input type="button" style="border-style: none; width: 250px; margin: 10px auto 20px auto;
                        font-size: 16px; color: #ffffff; line-height: 35px; height: 35px; background-color: #1ca53c;
                        text-align: center; font-family: 微软雅黑; cursor: pointer;" id="submitb" value="我要交卷"
                        name="submitb" onclick="UplaodPaper()"></div>
            </td>
        </tr>
    </table>
    
    <input name="frmUpExmNo" type="hidden" id="frmUpExmNo" size="20" value="SJ000132" />
    
    <input name="frmUpXmNo" type="hidden" id="frmUpXmNo" size="20" value="XM000187" />
    <input name="frmUpExmAllowJudge" type="hidden" id="frmUpExmAllowJudge" size="20" value="True" />
    <input name="frmPaperLock" type="hidden" id="frmPaperLock" size="20" value="False" />
    <input name="frmTimeMode" type="hidden" id="frmTimeMode" size="20" value="0" />
    <input name="frmExmClsID" type="hidden" id="frmExmClsID" size="20" value="1136" />
    <input name="frmStuNo" type="hidden" id="frmStuNo" size="20" value="130682200311195435" />
    <input name="frmTestDir" type="hidden" id="frmTestDir" size="20" value="c:\exam" />
    <input name="frmTestSysDir" type="hidden" id="frmTestSysDir" size="20" value="c:\examsys" />
    <input name="frmTestSysBakDir" type="hidden" id="frmTestSysBakDir" size="20" value="c:\examsys.bak" />
    <input name="frmMinutes" type="hidden" id="frmMinutes" size="20" value="120" />
    <input name="frmBeginTime" type="hidden" id="frmBeginTime" size="20" />
    <input name="frmExmType" type="hidden" id="frmExmType" size="20" value="False" />
    <input name="frmExmName" type="hidden" id="frmExmName" size="20" value="人工四级-技能-L" />
    <input name="frmClsName" type="hidden" id="frmClsName" size="20" value="20250730定州人工四级23人" />
    <input name="frmType" type="hidden" id="frmType" size="20" value="0" />
    <input name="hidIsExamSys" type="hidden" id="hidIsExamSys" value="1" />
    <input name="hidIsNewSys" type="hidden" id="hidIsNewSys" value="1" />
    <input name="frmcur_id" type="hidden" id="frmcur_id" size="20" />
    <input name="frmiswebcopy" type="hidden" id="frmiswebcopy" value="0" />
    <input name="frmcall_minutes" type="hidden" id="frmcall_minutes" value="0" />
    <input name="hidden" type="hidden" id="txtCommand" value="PaperNStart" />
    <input name="button" type="button" id="btnCallVB" style="visibility: hidden" />
    <input name="hidIsUnlockMin" type="hidden" id="hidIsUnlockMin" value="0" />
    <input name="hidCamera_minutes" type="hidden" id="hidCamera_minutes" value="0" />
    <input name="hidCameraSum" type="hidden" id="hidCameraSum" value="0" />
    
    <input name="qstnum" type="hidden" id="qstnum" value="0" />
    
    </form>
    <div id="bottom" style="display: none;">
        


     <table width="100%" border="0" align="center" cellpadding="0" cellspacing="0">
            <tr>
                <td width="164" height="30" style="background:url(images/main_71.gif)">
                    <table width="60%"  border="0" align="center" cellpadding="0" cellspacing="0">
                        <tr>
                            <td>
                                <div align="center">
                                版本：
                                     
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
                <td style="background:url(images/main_72.gif);">
                    <table width="100%"  border="0" align="center" cellpadding="0" cellspacing="0">
                        <tr>
                            <td width="99%">
                                <div align="right">
                                    新职汇
                                </div>
                            </td>
                            <td width="1%">　</td>
                        </tr>
                    </table>
                </td>
                <td width="15" style="background:url(images/main_74.gif);">　</td>
            </tr>
        </table>
   

    </div>
    <div id="screen">
    </div>
    <!--screen end-->
    <div class="popbox">
        <h2>
            消息<a class="close-btn" href="#">关闭</a></h2>
        <table border="0" cellpadding="0" cellspacing="0" width="100%">
            <tr>
                <td style="height: 80px;" valign="middle" align="center">
                    <div class="mainlist">
                    </div>
                </td>
            </tr>
            <tr class="popboxtr1">
                <td align="center">
                    <input id="Button" type="button" value="确 定" class="close-btn" style="width: 80px;
                        height: 25px" />
                </td>
            </tr>
        </table>
    </div>
    <!--popbox end-->
    <div id="Camera_popbox">
        <table border="0" cellpadding="0" cellspacing="0" width="100%">
            <tr>
                <td style="height: 80px;" valign="middle" align="center">
                    <div class="Camera_mainlist">
                    </div>
                </td>
            </tr>
            <tr class="popboxtr1">
                <td align="center">
                    <input id="Button1" type="button" value="确 定" onclick="ckCamInit_lock()" style="width: 80px;
                        height: 25px" />
                </td>
            </tr>
        </table>
    </div>

    <script type="text/javascript">
        jQuery(document).ready(function() {

            if (jQuery('#frmiswebcopy').val() == "0") {
                jQuery('body').bind("cut copy", function(e) {
                    e.preventDefault(); //取消事件的默认动作。
                });
            }

            jQuery('.close-btn').click(function() {
                jQuery('#screen').hide();
                jQuery('.popbox').hide();
                if (call_Inl != null) {
                    window.clearTimeout(call_Inl);
                }
                return false;
            });

            var heightold = parseInt(window.innerHeight);
            var heightnew = heightold - 390;
            jQuery("#msg1").css({ 'max-height': heightnew + 'px' });
        });

        jQuery.fn.center = function(loaded) {
            var obj = this;
            body_width = parseInt(jQuery(window).width());
            body_height = parseInt(window.innerHeight);
            block_width = parseInt(obj.width());
            block_height = parseInt(obj.height());

            left_position = parseInt((body_width / 2) - (block_width / 2) + jQuery(window).scrollLeft());
            if (body_width < block_width) { left_position = 0 + jQuery(window).scrollLeft(); };

            top_position = parseInt((body_height / 2) - (block_height / 2) + jQuery(window).scrollTop());
            if (body_height < block_height) { top_position = 0 + jQuery(window).scrollTop(); };

            if (!loaded) {

                obj.css({ 'position': 'absolute' });
                obj.css({ 'top': top_position, 'left': left_position });
                jQuery(window).bind('resize', function() {
                    obj.center(!loaded);
                });
                jQuery(window).bind('scroll', function() {
                    obj.center(!loaded);
                });

            } else {
                obj.stop();
                obj.css({ 'position': 'absolute' });
                obj.animate({ 'top': top_position }, 200, 'linear');
            }
        }
        //        function showwindow() {

        //            alert
        //              ("\n浏览器各高度：\n\n" +
        //                "可视区域高度:window.innerHeight" + window.innerHeight + "\n" +
        //                "可视区域高度:jQuery(window).height()" + jQuery(window).height() + "\n" +
        //                "文档body的高度jQuery(document.body).height():" + jQuery(document.body).height() + "\n" +
        //                "文档body的总高度 包括border padding margin  jQuery(document.body).outerHeight(true)" + jQuery(document.body).outerHeight(true) + "\n" +

        //                "获取滚动条到顶部的垂直高度jQuery(document).scrollTop():" + jQuery(document).scrollTop() + "\n"
        //   
        //              );
        //            showMsg("可视区域高度");

        //        }
        
    </script>

    <script type="text/javascript">

        jQuery(document).ready(function() {

            //循环页面中每个上传域
            jQuery('.uploder-container').each(function() {
                var qst_no = jQuery(this).attr("ids");
                var stu_no = jQuery("#frmStuNo").val();
                var test_id = jQuery("#frmExmClsID").val();
                var Uploader = Q.Uploader,
              formatSize = Q.formatSize,
              boxtarget = document.getElementById("upload-target-" + qst_no),
              boxView = document.getElementById("upload-image-view-" + qst_no);

                new Uploader({
                    url: "upload.ashx",
                    target: boxtarget,
                    view: boxView,
                    //auto: false,

                    allows: ".jpg,.png,.gif,.bmp,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.pdf,.rar",
                    data: { p_qst_no: qst_no, p_stu_no: stu_no, p_test_id: test_id },
                    //图片缩放
                    //scale: {
                    //要缩放的图片格式
                    //types: ".jpg",
                    //最大图片大小(width|height)
                    //maxWidth: 1024,
                    //maxHeight: 1024
                    //},

                    on: {
                        //添加之前触发
                        add: function(task) {
                            if (task.disabled) return alert("允许上传的文件格式为：" + this.ops.allows);
                        },
                        //图片预览后触发
                        preview: function(data) {
                            //log(data.task.name + " : " + data.src);
                            //                         alert("图片预览后触发");
                        },
                        //图片压缩后触发,如果图片或浏览器不支持压缩,则不触发
                        scale: function(data) {
                            //log(data.task.name + " : 已压缩！");
                        },
                        remove: function(task) {
                            //alert("删除操作");
                        }        //移除上传任务后触发
                    }
                });
            });
        });

        function ShowBigImg(_this) {
            var src = _this.attr("src"); //获取当前点击的pimg元素中的src属性  
            jQuery("#bigimg").attr("src", src); //设置#bigimg元素的src属性  
            /*获取当前点击图片的真实大小，并显示弹出层及大图*/
            jQuery("<img/>").attr("src", src).load(function() {
                var windowW = jQuery(window).width(); //获取当前窗口宽度  
                var windowH = window.innerHeight; //获取当前窗口高度  
                var realWidth = this.width; //获取图片真实宽度  
                var realHeight = this.height; //获取图片真实高度  
                var imgWidth, imgHeight;
                var scale = 0.8; //缩放尺寸，当图片真实宽度和高度大于窗口宽度和高度时进行缩放  
                if (realHeight > windowH * scale) {//判断图片高度  
                    imgHeight = windowH * scale; //如大于窗口高度，图片高度进行缩放  
                    imgWidth = imgHeight / realHeight * realWidth; //等比例缩放宽度  
                    if (imgWidth > windowW * scale) {//如宽度扔大于窗口宽度  
                        imgWidth = windowW * scale; //再对宽度进行缩放  
                    }
                } else if (realWidth > windowW * scale) {//如图片高度合适，判断图片宽度  
                    imgWidth = windowW * scale; //如大于窗口宽度，图片宽度进行缩放  
                    imgHeight = imgWidth / realWidth * realHeight; //等比例缩放高度  
                } else {//如果图片真实高度和宽度都符合要求，高宽不变  
                    imgWidth = realWidth;
                    imgHeight = realHeight;
                }
                jQuery("#bigimg").css("width", imgWidth); //以最终的宽度对图片缩放  
                var w = (windowW - imgWidth) / 2; //计算图片与窗口左边距  
                var h = (windowH - imgHeight) / 2; //计算图片与窗口上边距  
                jQuery("#innerdiv").css({ "top": h, "left": w }); //设置#innerdiv的top和left属性  
                jQuery("#outerdiv").fadeIn("fast"); //淡入显示#outerdiv及.pimg  
            });
            jQuery("#outerdiv").click(function() {//再次点击淡出消失弹出层  
                jQuery(this).fadeOut("fast");
            });
        }
        function downloadfile(rstfileid) {

        }
    </script>

    <script type="text/javascript">
        function removepic(rstpicid) {
            jQuery.ajax({
                type: "post",
                url: "aspAjax/AjaxDelQstRstPic.aspx",
                data: { p_rstpicid: rstpicid },
                success: function(msg) {
                    jQuery("#u-item-" + rstpicid).remove();
                }
            });

        }
        function showfile(rstpicid, opt) {
            if (opt == "0") {
                var _this = jQuery("#u-item-" + rstpicid).find("img"); //将当前的pimg元素作为_this传入函数  
                ShowBigImg(_this);
            }
            else if (opt == "1") {
                download(rstpicid);
            }
        }
        function download(rstpicid) {
            var url = "ajaxGetResource.aspx?ftype=filedown&qst_no=" + rstpicid;
            var elemIF = document.createElement("iframe");
            elemIF.src = url;
            elemIF.style.display = "none";
            document.body.appendChild(elemIF);
        }
        //按比例缩放图片
        function AutoResizeImage(maxWidth, maxHeight, objImg) {
            var img = new Image();
            img.src = objImg.src;
            var hRatio;
            var wRatio;
            var Ratio = 1;
            var w = img.width;
            var h = img.height;
            wRatio = maxWidth / w;
            hRatio = maxHeight / h;
            if (maxWidth == 0 && maxHeight == 0) {
                Ratio = 1;
            } else if (maxWidth == 0) {//
                if (hRatio < 1) Ratio = hRatio;
            } else if (maxHeight == 0) {
                if (wRatio < 1) Ratio = wRatio;
            } else if (wRatio < 1 || hRatio < 1) {
                Ratio = (wRatio <= hRatio ? wRatio : hRatio);
            }
            if (Ratio < 1) {
                w = w * Ratio;
                h = h * Ratio;
            }
            objImg.height = h;
            objImg.width = w;
        }
        
    </script>
    
    <script type="text/javascript">
        //手机端生成二维码
        function phone_uplod(self) {

            var qst_no = jQuery(self).attr("ids");
            var stu_no = jQuery("#frmStuNo").val();
            var test_id = jQuery("#frmExmClsID").val();

            var url = "PhoneCodeUpladImg.aspx?stuno=" + stu_no + "&qno=" + qst_no + "&tid=" + test_id + "";

            window.open(url, '_blank', "toolbar=no,width=400,height=300,directories=no, location=no,status=no,scrollbars=yes,resizable=yes,menubar=no,top=" + (screen.height - 300) / 2 + ",left=" + (screen.width - 400) / 2 + "");

           
            
        }

        //创建二维码
        function createQRCode(id, url, width, height, src) {
            jQuery('#' + id).empty();
            jQuery('#' + id).qrcode({
                render: 'canvas',
                text: url,
                width: width,              //二维码的宽度  
                height: height,            //二维码的高度  
                imgWidth: width / 4,         //图片宽
                imgHeight: height / 4,       //图片高
                src: src            //图片中央的二维码
            });
        }

         
    </script>

    <div id="outerdiv" style="position: fixed; top: 0; left: 0; background: rgba(0,0,0,0.7);
        z-index: 2; width: 100%; height: 100%; display: none;">
        <div id="innerdiv" style="position: absolute;">
            <img id="bigimg" style="border: 5px solid #fff;" src="" />
        </div>
    </div>
    <div class="tankuang_box">
        <div class="tankuang">
            <div style="height: 40px; line-height: 40px; text-align: center; background: #eee;">
                试题纠错
            </div>
            <div style="height: 40px; line-height: 40px; width: 550px; margin: auto;">
                <div style="float: left; margin-right: 10px;">
                    本题何错之有？</div>
                <div class="topa" onclick="inputcorqst('html')">
                    题干错误</div>
                <div class="topa" onclick="inputcorqst('anw')">
                    选项错误</div>
                <div class="topa" onclick="inputcorqst('pnt')">
                    无法得分</div>
            </div>
            <div style="height: 220px; width: 550px; margin: auto;">
                <textarea id="corcont" style="height: 210px; width: 100%; resize: none; padding: 4px;"></textarea>
            </div>
            <div style="height: 40px; line-height: 40px; width: 190px; margin: 10px auto;">
                <div class="tansave" onclick="savejiucuo()">
                    提交
                </div>
                <div class="tanclose" onclick="closetankuang()">
                    取消
                </div>
            </div>
        </div>
        <input type="hidden" id="hidqstno" />
        <input type="hidden" id="hidqstid" />
        <input type="hidden" id="hidqstcont" />
    </div>
</body>
</html>
