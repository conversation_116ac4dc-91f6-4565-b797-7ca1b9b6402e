// ==UserScript==
// @name         chuji
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  自动匹配题库答案并选择，使用精确匹配避免选错，添加反侦查功能模拟真人操作，简化UI只显示核心信息
// <AUTHOR> name
// @match        http://************/*
// @grant        none
// ==/UserScript==

(function() {
    'use strict';

    // 从远程加载题库数据
    let questionBank = [];
    const questionBankUrl = 'https://cdn.jsdelivr.net/gh/asd2261/chujitk7-23@main/chuji.json';
    
    // 全局变量，用于控制答题过程
    let isAnswering = false;
    let pauseAnswering = false;
    let currentQuestionRows = [];
    let currentIndex = 0;
    let totalQuestions = 0;
    
    // 添加全局变量
    let autoMode = true;
    let idleTimer = null;
    const IDLE_TIMEOUT = 10000; // 10秒
    
    // 创建悬浮窗口
    function createFloatingWindow() {
        // 创建悬浮窗口的主体
        const floatDiv = document.createElement('div');
        floatDiv.id = 'answerAssistant';
        floatDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            width: 300px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            z-index: 10000;
            font-family: Arial, sans-serif;
            overflow: hidden;
            transition: box-shadow 0.3s ease;
        `;

        // 创建标题栏
        const titleBar = document.createElement('div');
        titleBar.style.cssText = `
            background-color: #4CAF50;
            color: white;
            padding: 10px;
            font-weight: bold;
            cursor: move;
            display: flex;
            justify-content: space-between;
            align-items: center;
        `;
        titleBar.textContent = '新职汇答题助手';

        // 创建关闭按钮
        const closeButton = document.createElement('span');
        closeButton.innerHTML = '&times;';
        closeButton.style.cssText = `
            cursor: pointer;
            font-size: 20px;
        `;
        closeButton.onclick = function() {
            document.body.removeChild(floatDiv);
        };
        titleBar.appendChild(closeButton);

        // 创建内容区域
        const contentDiv = document.createElement('div');
        contentDiv.style.cssText = `
            padding: 15px;
        `;

        // 创建开始/暂停按钮
        const startPauseButton = document.createElement('button');
        startPauseButton.id = 'startPauseButton';
        startPauseButton.textContent = '开始答题';
        startPauseButton.style.cssText = `
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            width: 100%;
            margin-bottom: 10px;
            font-weight: bold;
            transition: background-color 0.3s ease;
        `;
        startPauseButton.onclick = toggleAnswering;

        // 创建当前题目信息显示区域
        const currentQuestionInfo = document.createElement('div');
        currentQuestionInfo.id = 'currentQuestionInfo';
        currentQuestionInfo.style.cssText = `
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            font-size: 14px;
            line-height: 1.4;
            max-height: 150px;
            overflow-y: auto;
            margin-bottom: 10px;
        `;
        currentQuestionInfo.innerHTML = '准备就绪，点击开始答题按钮开始自动答题。';

        // 创建状态信息区域
        const statusInfo = document.createElement('div');
        statusInfo.id = 'statusInfo';
        statusInfo.style.cssText = `
            font-size: 12px;
            color: #666;
            text-align: center;
        `;
        statusInfo.textContent = '加载题库中...';

        // 组装悬浮窗
        contentDiv.appendChild(startPauseButton);
        contentDiv.appendChild(currentQuestionInfo);
        contentDiv.appendChild(statusInfo);
        floatDiv.appendChild(titleBar);
        floatDiv.appendChild(contentDiv);
        document.body.appendChild(floatDiv);

        // 加载远程题库
        fetch(questionBankUrl)
            .then(response => {
                if (!response.ok) {
                    throw new Error('网络响应不正常');
                }
                return response.json();
            })
            .then(data => {
                questionBank = data.map(item => ({
                    question: item.question,
                    correctAnswer: item.answer
                }));
                statusInfo.textContent = `题库加载完成，共 ${questionBank.length} 题`;
            })
            .catch(error => {
                statusInfo.textContent = `题库加载失败: ${error.message}`;
                statusInfo.style.color = '#f44336';
            });

        // 实现拖拽功能
        let isDragging = false;
        let offsetX, offsetY;

        titleBar.addEventListener('mousedown', function(e) {
            isDragging = true;
            offsetX = e.clientX - floatDiv.getBoundingClientRect().left;
            offsetY = e.clientY - floatDiv.getBoundingClientRect().top;
            floatDiv.style.boxShadow = '0 8px 16px rgba(0,0,0,0.3)';
        });

        document.addEventListener('mousemove', function(e) {
            if (!isDragging) return;

            const x = e.clientX - offsetX;
            const y = e.clientY - offsetY;

            // 确保窗口不会被拖出视口
            const maxX = window.innerWidth - floatDiv.offsetWidth;
            const maxY = window.innerHeight - floatDiv.offsetHeight;

            floatDiv.style.left = Math.max(0, Math.min(x, maxX)) + 'px';
            floatDiv.style.top = Math.max(0, Math.min(y, maxY)) + 'px';
            // 修复拖拽问题：移除right属性，确保位置由left和top控制
            floatDiv.style.right = '';
        });

        document.addEventListener('mouseup', function() {
            isDragging = false;
            floatDiv.style.boxShadow = '0 4px 8px rgba(0,0,0,0.2)';
        });
    }

    // 辅助函数：生成随机延迟时间
    function getRandomDelay(min, max) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }

    // 添加鼠标事件处理
    function initMouseEvents() {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('click', handleMouseClick);
    }

    function handleMouseMove() {
      resetIdleTimer();

      if(autoMode) {
        autoMode = false;
        console.log('切换到手动模式');
        document.getElementById('startPauseButton').textContent = '暂停答题(手动模式)';
        document.getElementById('startPauseButton').style.backgroundColor = '#ff9800';
      }
    }

    function handleMouseClick(e) {
      // 检查是否点击了正确答案
      const correctOption = document.querySelector('[data-correct="true"]');
      if(correctOption && correctOption.contains(e.target)) {
        // 显示忙状态
        document.body.style.cursor = 'wait';
        setTimeout(() => {
          document.body.style.cursor = '';
        }, 1000);
      }
    }

    function resetIdleTimer() {
      clearTimeout(idleTimer);
      idleTimer = setTimeout(() => {
        if(!autoMode) {
          autoMode = true;
          console.log('10秒无操作，切换回自动模式');
          document.getElementById('startPauseButton').textContent = '暂停答题';
          document.getElementById('startPauseButton').style.backgroundColor = '#f44336';
        }
      }, IDLE_TIMEOUT);
    }

    // 辅助函数：模拟鼠标移动和点击
    function simulateHumanClick(element) {
        return new Promise(resolve => {
            // 先模拟鼠标悬停
            const mouseoverEvent = new MouseEvent('mouseover', {
                bubbles: true,
                cancelable: true,
                view: window
            });
            element.dispatchEvent(mouseoverEvent);

            // 随机延迟后模拟点击
            setTimeout(() => {
                const clickEvent = new MouseEvent('click', {
                    bubbles: true,
                    cancelable: true,
                    view: window
                });
                element.dispatchEvent(clickEvent);
                resolve();
            }, getRandomDelay(300, 1200)); // 随机延迟300-1200ms
        });
    }

    // 开始/暂停答题切换函数
    function toggleAnswering() {
        const button = document.getElementById('startPauseButton');

        if (!isAnswering) {
            // 开始答题
            isAnswering = true;
            pauseAnswering = false;
            button.textContent = '暂停答题';
            button.style.backgroundColor = '#f44336';

            // 重置计数器
            currentQuestionRows = document.querySelectorAll('#papertable tr[id^="tr"]');
            totalQuestions = currentQuestionRows.length;
            currentIndex = 0;

            // 开始答题过程
            processNextQuestion();
        } else {
            if (pauseAnswering) {
                // 继续答题
                pauseAnswering = false;
                button.textContent = '暂停答题';
                button.style.backgroundColor = '#f44336';
                processNextQuestion();
            } else {
                // 暂停答题
                pauseAnswering = true;
                button.textContent = '继续答题';
                button.style.backgroundColor = '#ff9800';
            }
        }
    }

    // 处理下一个问题
    async function processNextQuestion() {
        if (pauseAnswering || !isAnswering || !autoMode) return;

        if (currentIndex >= totalQuestions) {
            // 答题完成
            finishAnswering();
            return;
        }

        const row = currentQuestionRows[currentIndex];
        currentIndex++;

        // 添加滚动到当前题目的功能
        row.scrollIntoView({ behavior: 'smooth', block: 'center' });

        // 获取题目文本
        const questionDiv = row.querySelector('div[name^="DIV"]');
        if (!questionDiv) {
            processNextQuestion();
            return;
        }

        const questionTable = questionDiv.querySelector('table');
        if (!questionTable) {
            processNextQuestion();
            return;
        }

        const questionTd = questionTable.querySelector('td[valign="top"][style*="width"]');
        if (!questionTd) {
            processNextQuestion();
            return;
        }

        // 提取题目文本
        const paragraphs = questionTd.querySelectorAll('p');
        if (paragraphs.length < 2) {
            processNextQuestion();
            return;
        }

        const questionText = paragraphs[0].textContent.trim();
        // 移除题号和括号
        const cleanQuestion = questionText.replace(/^\d+、\s*|（\s*）\s*。*$/g, '');

        // 随机暂停一段时间，模拟阅读题目
        await new Promise(resolve => setTimeout(resolve, getRandomDelay(1000, 3000)));

        // 提取所有选项文本和对应的按钮
        const optionsMap = new Map();

        // 检查是否是多选题（查找checkbox类型的输入）
        const checkboxes = row.querySelectorAll('input[type="checkbox"]');
        const isMultipleChoice = checkboxes.length > 0;

        // 根据题目类型获取选项
        const options = isMultipleChoice ? checkboxes : row.querySelectorAll('input[type="radio"]');

        // 判断是否是判断题
        const isJudgmentQuestion = options.length === 2 &&
            ((options[0].nextSibling && options[0].nextSibling.textContent.includes('正确')) ||
             (options[1].nextSibling && options[1].nextSibling.textContent.includes('错误')));

        // 检查是否包含"以上都是"或类似选项，但不再直接跳过
        let hasAllOfTheAboveOption = false;
        let allOfTheAboveOptionContent = '';
        let allOfTheAboveOption = null;

        for (let i = 0; i < paragraphs.length; i++) {
            const text = paragraphs[i].textContent.trim();
            // 检测各种"以上都..."的表述
            if (text.includes('以上都是') || 
                text.includes('以上都对') || 
                text.includes('以上都不对') || 
                text.includes('以上都不是') ||
                text.includes('以上都错') ||
                text.includes('以上都正确')) {
                hasAllOfTheAboveOption = true;
                // 找到对应的选项
                const optionIndex = i - 1; // 段落索引比选项索引大1
                if (optionIndex >= 0 && optionIndex < options.length) {
                    allOfTheAboveOptionContent = text;
                    allOfTheAboveOption = options[optionIndex];
                }
                break;
            }
        }

        options.forEach(option => {
            // 获取选项标签（A、B、C、D）
            const optionLabel = option.nextSibling ? option.nextSibling.textContent.trim() : '';

            // 获取选项内容
            const optionIndex = Array.from(options).indexOf(option);
            if (optionIndex >= 0 && optionIndex + 1 < paragraphs.length) {
                const optionText = paragraphs[optionIndex + 1].textContent.trim();
                // 提取选项内容（去除前面的A.、B.等标识）
                const optionContent = optionText.replace(/^\s*[A-D]\.\s*/, '').trim();
                optionsMap.set(optionContent, option);
            }
        });

        let found = false;
        let matchedQuestion = null;
        let correctAnswer = '';

        // 遍历题库中的每个问题
        for (let i = 0; i < questionBank.length && !found; i++) {
            const qbItem = questionBank[i];

            // 改进匹配逻辑：规范化处理题目文本，移除括号和多余空格
            const normalizedQuestion = cleanQuestion.replace(/（\s*）/g, '').replace(/\s+/g, ' ').trim();
            const normalizedBankQuestion = qbItem.question.replace(/（\s*）/g, '').replace(/\s+/g, ' ').trim();

            // 使用更灵活的匹配方式
            if (normalizedQuestion.includes(normalizedBankQuestion) ||
                normalizedBankQuestion.includes(normalizedQuestion) ||
                // 计算相似度，如果相似度超过80%也认为匹配
                calculateSimilarity(normalizedQuestion, normalizedBankQuestion) > 0.8) {

                matchedQuestion = qbItem;
                correctAnswer = qbItem.correctAnswer;
                
                // 检查答案是否包含"；；"分隔符，用于判断是否为"以上都对/以上都不对"类型的题目
                const containsSeparator = correctAnswer.includes('；；');
                
                // 如果答案包含分隔符且存在"以上都是"类选项，则选择该选项
                if (containsSeparator && hasAllOfTheAboveOption && allOfTheAboveOption) {
                    // 检查"以上都是"选项的内容是否与答案的意图一致
                    const isPositiveAllOption = allOfTheAboveOptionContent.includes('以上都是') || 
                                               allOfTheAboveOptionContent.includes('以上都对') || 
                                               allOfTheAboveOptionContent.includes('以上都正确');
                    
                    const isNegativeAllOption = allOfTheAboveOptionContent.includes('以上都不对') || 
                                               allOfTheAboveOptionContent.includes('以上都不是') || 
                                               allOfTheAboveOptionContent.includes('以上都错');
                    
                    // 根据答案内容判断是否选择"以上都是"选项
                    // 如果答案包含多个分隔的项，且选项是肯定性的"以上都是"，则选择该选项
                    if ((correctAnswer.split('；；').length > 1 && isPositiveAllOption) ||
                        // 或者如果答案明确表示"以上都不对"且选项也是否定性的，则选择该选项
                        (correctAnswer.includes('以上都不对') && isNegativeAllOption)) {
                        // 模拟人类点击行为
                        await simulateHumanClick(allOfTheAboveOption);
                        allOfTheAboveOption.checked = true;
                        found = true;
                    } else {
                        // 否则按照普通题目处理
                        // 处理逻辑保持不变
                    }
                }
                // 判断题的特殊处理
                else if (isJudgmentQuestion) {
                    // 判断题的正确答案通常是"正确"或"错误"
                    const correctOption = correctAnswer.includes('正确') ? options[0] : options[1];

                    // 模拟人类点击行为
                    await simulateHumanClick(correctOption);
                    correctOption.checked = true;
                    found = true;
                }
                // 处理多选题的情况
                else if (isMultipleChoice) {
                    // 多选题的正确答案可能是多个
                    // 如果答案包含"；；"分隔符，使用它来分割
                    const correctAnswers = containsSeparator ? 
                        qbItem.correctAnswer.split('；；').map(ans => ans.trim()) :
                        qbItem.correctAnswer.split('、').map(ans => ans.trim());

                    // 遍历所有选项，选中正确答案
                    for (const [optionContent, option] of optionsMap.entries()) {
                        // 规范化处理选项内容，移除多余空格
                        const normalizedOptionContent = optionContent.replace(/\s+/g, ' ').trim();
                        
                        // 检查当前选项是否是正确答案之一
                        const isCorrect = correctAnswers.some(answer => {
                            // 规范化处理答案，移除多余空格
                            const normalizedAnswer = answer.replace(/\s+/g, ' ').trim();
                            return normalizedOptionContent === normalizedAnswer ||
                                   normalizedOptionContent.includes(normalizedAnswer) ||
                                   normalizedAnswer.includes(normalizedOptionContent);
                        });

                        if (isCorrect) {
                            // 模拟人类点击行为
                            await simulateHumanClick(option);
                            option.checked = true;
                            found = true;
                        }
                    }
                } else {
                    // 精确匹配：遍历所有选项，寻找与正确答案完全匹配的选项
                    for (const [optionContent, option] of optionsMap.entries()) {
                        // 规范化处理选项内容和正确答案，移除多余空格
                        const normalizedOptionContent = optionContent.replace(/\s+/g, ' ').trim();
                        const normalizedCorrectAnswer = qbItem.correctAnswer.replace(/\s+/g, ' ').trim();
                        
                        // 使用规范化后的文本进行精确匹配
                        if (normalizedOptionContent === normalizedCorrectAnswer) {
                            // 模拟人类点击行为
                            await simulateHumanClick(option);
                            option.checked = true;

                            found = true;
                            break;
                        }
                    }

                    // 如果没有找到精确匹配，尝试部分匹配
                    if (!found) {
                        for (const [optionContent, option] of optionsMap.entries()) {
                            // 规范化处理选项内容和正确答案，移除多余空格
                            const normalizedOptionContent = optionContent.replace(/\s+/g, ' ').trim();
                            const normalizedCorrectAnswer = qbItem.correctAnswer.replace(/\s+/g, ' ').trim();
                            
                            if (normalizedOptionContent.includes(normalizedCorrectAnswer) ||
                                normalizedCorrectAnswer.includes(normalizedOptionContent)) {
                                // 模拟人类点击行为
                                await simulateHumanClick(option);
                                option.checked = true;

                                found = true;
                                break;
                            }
                        }
                    }
                }
            }
        }

        // 添加一个计算字符串相似度的函数
        function calculateSimilarity(str1, str2) {
            // 简单的相似度计算：共同字符数 / 较长字符串长度
            const set1 = new Set(str1);
            const set2 = new Set(str2);
            const intersection = new Set([...set1].filter(x => set2.has(x)));
            return intersection.size / Math.max(set1.size, set2.size);
        }

        if (!found) {
            correctAnswer = '未找到匹配答案';
        }

        // 更新当前题目信息
        document.getElementById('currentQuestionInfo').innerHTML =
            `当前题目：${cleanQuestion}<br>答案：${correctAnswer}`;

        // 随机暂停，模拟思考时间
        if (Math.random() < 0.3) { // 30%概率进行较长暂停
            await new Promise(resolve => setTimeout(resolve, getRandomDelay(3000, 8000)));
        } else {
            await new Promise(resolve => setTimeout(resolve, getRandomDelay(800, 2000)));
        }

        // 处理下一个问题
        if (!pauseAnswering) {
            processNextQuestion();
        }
    }

    // 完成答题
    function finishAnswering() {
        isAnswering = false;
        document.getElementById('startPauseButton').textContent = '开始答题';
        document.getElementById('startPauseButton').style.backgroundColor = '#4CAF50';
        document.getElementById('currentQuestionInfo').innerHTML = '答题完成！';
    }

    // 页面加载完成后创建悬浮窗
    window.addEventListener('load', () => {
        if (window.location.href.includes('************')) {
            // 随机延迟创建，避免固定时间特征
            setTimeout(createFloatingWindow, getRandomDelay(1500, 3000));
            // 初始化鼠标事件监听
            initMouseEvents();
        }
    });
})();