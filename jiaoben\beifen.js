// ==UserScript==
// @name         新职汇自动答题助手
// @namespace    http://tampermonkey.net/
// @version      1.6
// @description  自动匹配题库答案并选择，使用精确匹配避免选错，添加反侦查功能模拟真人操作，简化UI只显示核心信息
// <AUTHOR> name
// @match        http://************/*
// @grant        none
// ==/UserScript==

(function() {
    'use strict';

    // 题库数据 - 使用题目内容和正确答案内容进行匹配
    const questionBank = [
        // 单选题
        {
            question: '以下关于数据的描述错误的是',
            correctAnswer: '数据本身能完全表达其内容，无需经过解释'
        },
        {
            question: '发现在某个因素发生变化时，另一个因素也一直跟着这个因素变化。此种方法属于归纳分析方法中的',
            correctAnswer: '共变法'
        },
        {
            question: '是用适当的分析方法及工具，对处理过的数据进行分析，提取有价值的信息，形成有效结论的过程',
            correctAnswer: '数据分析'
        },
        {
            question: '职业守则是的表现形式',
            correctAnswer: '职业道德'
        },
        {
            question: '评估数据质量一般会从数据的方面考虑',
            correctAnswer: '以上都对',
            isAllCorrect: true
        },
        {
            question: '分析法代表企业优势（strength）、劣势（weakness）、机会（opportunity）和威胁（threats）。该分析实际上是对企业内外部条件各方面进行综合和概括',
            correctAnswer: 'SWOT'
        },
        {
            question: '帮助用户节省时间，产品自身提供价值，核心指标应该聚焦到判断工具的使用率，描述的是常见的互联网业务类型中构建的四个象限中的',
            correctAnswer: '工具类业务'
        },
        {
            question: '是软件服务、应用和连接器的集合，它们协同工作以将相关数据来源转换为连贯的视觉逼真的交互式见解',
            correctAnswer: 'Power BI'
        },
        {
            question: '对市场上的产品进行特征分析，找准切入点属于聚类分析中的应用',
            correctAnswer: '商业'
        },
        {
            question: '关于数据质量的概述说法错误的是',
            correctAnswer: '数据质量是指数据的格式与大小'
        },
        {
            question: '将总体中各单位归并成若干个互不交叉、互不重复的群，然后所有群中随机抽取若干个群，对这些群内所有个体或单元均进行抽查。该抽样属于',
            correctAnswer: '整群抽样'
        },
        {
            question: '大数据与人工智能的关系说法错误的是',
            correctAnswer: '大数据不为人工智能提供资源和能源，只为人工智能提供算力'
        },
        {
            question: '数据结构不规则或不完整，没有预定义的数据模型，不方便用 结构化形式 和 半结构化形式 来表现的数据是',
            correctAnswer: '非结构化数据'
        },
        {
            question: '通过技术代码在App接口中进行埋点的方式称之为',
            correctAnswer: '代码埋点'
        },
        {
            question: '在使用数据过程中，要确保数据分类分级的合法性和合规性，保护数据主体的权益，并降低数据处理过程中的风险，这属于数据分类分级的原则',
            correctAnswer: '合法合规'
        },
        {
            question: '通过对整个标注阶段涉及的质检问题进行分析与总结的报告属于报告',
            correctAnswer: '项目质量总结'
        },
        {
            question: '关于归一化和标准化说法错误的是',
            correctAnswer: '归一化和标准化是等价的，没有任何区别'
        },
        {
            question: '审核数据是否符合逻辑，内容是否合理，各项目或数字之间有无相互矛盾的现象。属于检查方法',
            correctAnswer: '逻辑'
        },
        {
            question: '数据分析与挖掘过程中比较基础且重要的一个环节是数据采集，数据采集是数据分析挖掘的',
            correctAnswer: '根基'
        },
        {
            question: '关于验收报告主要要求说法错误的是',
            correctAnswer: '无需明确我方对其做出的承诺'
        },
        {
            question: '在数据质量管理流程步骤中，第一步需要做的是',
            correctAnswer: '分析业务需求、明确数据应用环境'
        },
        {
            question: '对数据进行分类分级可采取以下哪些技术',
            correctAnswer: '人工、系统、人工+智能'
        },
        {
            question: '可以使用像思维导图类的工具来进行知识整理，从而对单元知识点的系统掌握，该种知识整理方法为整理',
            correctAnswer: '知识框架'
        },
        {
            question: '为智能产品适配应用场景的设计思路要清楚人工智能场景还具备、替代性发挥人的主体性、交互性以及集成化的特点和能力',
            correctAnswer: '智能化'
        },
        {
            question: '以下关于半结构化数据描述正确的是',
            correctAnswer: '半结构化数据是介于结构化和非结构化之间的数据，XML和HTML等格式数据属于半结构化数据'
        },
        {
            question: '以下关于人工智能与数据分析关系描述错误的是',
            correctAnswer: '数据分析更多的是基于现有数据，而人工智能则是基于历史数据'
        },
        {
            question: '在抽样时，将总体分成互不交叉的层，然后按照一定的比例，从各层独立地抽取一定数量的个体，将各层取出的个体合在一起作为样本。该种抽样方法是抽样',
            correctAnswer: '分层'
        },
        {
            question: '以下属于理解数据的方法的是',
            correctAnswer: '审查数据的维度、通过描述性统计分析数据、理解数据属性的相关性'
        },
        {
            question: '在数据采集、处理、传输等技术环节中的异常造成的数据质量问题属于因素',
            correctAnswer: '技术'
        },
        {
            question: '人工智能领域的研究包括',
            correctAnswer: '机器人、自然语音处理、专家系统'
        },
        {
            question: '数据审核的目的是',
            correctAnswer: '保障数据质量'
        },
        {
            question: '对于实时监测性质的系统，要能够实时采集数据并上报，是对数据采集的要求',
            correctAnswer: '及时'
        },
        {
            question: '根据《中华人民共和国网络安全法》的规定，负责统筹协调网络安全工作和相关监督管理工作',
            correctAnswer: '国家网信部门'
        },
        {
            question: '以下关于数据整理步骤描述正确的是',
            correctAnswer: '根据研究目的设计整理方案、数据分组和汇总，并计算各项指标、统计资料的积累、保管和公布'
        },
        {
            question: '以下属于聚类方法的是',
            correctAnswer: '划分式聚类方法、层次化聚类方法、基于密度的聚类方法'
        },
        {
            question: '通过机器学习，包括使用已训练的模型进行检查，或使用迁移学习等方法对人工标注的数据做质量检查，实现全自动或者辅助人工质量检查。该检验方法属于',
            correctAnswer: '机器检验'
        },
        {
            question: '数据标注团队内部自对自检，团队各小组间互相检查，各小组长对组内数据质量负责，属于质量监控中的什么检查体系',
            correctAnswer: '相互协作式自检体系'
        },
        {
            question: '数据标注是对收集到的，未处理的初级数据，包括等进行加工处理，并转换为机器可识别信息的过程',
            correctAnswer: '语音、图像、文本'
        },
        {
            question: '有一款电子表格软件，软件由一系列行和列构成，形成一个个网格，内置功能对存储单元格中的数据进行计算、分析等操作。上述描述的是什么工具？',
            correctAnswer: 'Excel'
        },
        {
            question: '完成标注训练的整个工作流程，通常需要经历 数据准备 、、 数据进化 三个环节',
            correctAnswer: '数据标注'
        },
        {
            question: '对数据不加遗漏逐个检测属于检验',
            correctAnswer: '全样'
        },
        {
            question: '下列哪一项是合同法的立法宗旨',
            correctAnswer: '保护劳动者的合法权益'
        },
        {
            question: '关于人工智能训练师的基础职业素养和能力，主要要求有',
            correctAnswer: '具备一定的逻辑思维能力、做事细致认真，有耐心、对数据敏感，具备一定的法律安全意识'
        },
        {
            question: '管理数据的整个生命周期，包括数据的采集、存储、处理、分析和应用等环节，属于智能数据平台技术视角需要解决的问题',
            correctAnswer: '过程管理'
        },
        {
            question: '通过点击交互，在产品界面上直接进行埋点，先分析，再圈选的数据采集技术属于埋点采集',
            correctAnswer: '可视化'
        },
        {
            question: 'PEST模型分析法中的 S 指的是',
            correctAnswer: '社会'
        },
        {
            question: '某公司对内部的设备从1、2、3...n按照连续的数字进行编号，该公司的设备编号登记表就是一份数据，该数据中设备编号所在列是',
            correctAnswer: '连续的值'
        },
        {
            question: '以下关于数据的特点描述错误的是',
            correctAnswer: '数据没有记录形式'
        },
        {
            question: '以下关于数据采集工具的说法错误的是',
            correctAnswer: 'Flume是唯一的数据采集工具'
        },
        {
            question: '是计算机科学的一个分支，它企图了解智能的实质，并生产出一种新的能以与人类智能相似的方式做出反应的智能机器',
            correctAnswer: '人工智能'
        },
        {
            question: '标准制定、标准执行、在标准制定和执行过程中的组织保障 ，描述的是数据处理流程优化方法中的',
            correctAnswer: '标准化和组织保障'
        },
        {
            question: '质量控制流程分为三个阶段，其中有一个阶段主要是进行正式的标注/验收工作，请问该阶段是',
            correctAnswer: '质检与验收阶段'
        },
        {
            question: '鲁棒性是指算法对于异常情况、噪声等的处理能力。k-means算法对噪声的处理能力较差，往往可能会因为某些噪声点，将结果错误地分配到某个聚类中心，从而影响整体的聚类结果。由此可见',
            correctAnswer: 'k-means算法的鲁棒性较差'
        },
        {
            question: '数据质量需求来源于',
            correctAnswer: '数据应用、系统要求、数据管理'
        },
        {
            question: '以下关于知识整理的重要性说法错误的是',
            correctAnswer: '知识整理最终目的只是为了整理'
        },
        {
            question: '我们在梳理数据仓库分层架构图时，以图表的形式对其进行整理，该知识整理方法属于',
            correctAnswer: '图表式整理'
        },
        {
            question: '已经存储在数据仓库中的数据发现质量问题，通过使用质量管控工具解决质量问题，是在数据阶段中提升数据质量',
            correctAnswer: '存储'
        },
        {
            question: '智能数据平台是由数据流程和流程两大主体共同构成的解决方案',
            correctAnswer: '业务'
        },
        {
            question: '数据分类分级实施标准中的常见的，是指在网络环境中产生、传输、存储和处理的各种数据',
            correctAnswer: '网络数据'
        },
        {
            question: '聚类分析中需要保序的方法是聚类',
            correctAnswer: '有序'
        },
        {
            question: '写一时事的评论文章时可以围绕某一热点，从经济、文化、哲学、法律、道德等多角度、全方位进行解读。这个就是所谓的整理',
            correctAnswer: '专题'
        },
        {
            question: '关于Power BI工具的描述错误的是',
            correctAnswer: 'Power BI工具是一款简单的P图工具，不可以用来做数据分析'
        },
        {
            question: '以下属于数据整理工具的是',
            correctAnswer: 'Excel、Python、Kettle'
        },
        {
            question: '数据采集的主要方法有',
            correctAnswer: '数据众包采集、数据行业合作、传感器数据采集'
        },
        {
            question: '某员工食堂通过发放调查问卷的方式收集员工对食堂的反馈情况，这属于数据采集中的方法',
            correctAnswer: '问卷调查'
        },
        {
            question: '以下关于归纳分析方法说法正确的是',
            correctAnswer: '归纳分析的方法有求同法、求异法、共用法、共变法、剩余法'
        },
        {
            question: '不同数据的文件格式是不同的，以不同来进行区分',
            correctAnswer: '文件扩展名'
        },
        {
            question: '指标体系是将的具有相互联系的指标，系统化的组织起来',
            correctAnswer: '零散'
        },
        {
            question: '道德的特点不包括',
            correctAnswer: '天赋性'
        },
        {
            question: '企业拥有并保护的非公开信息，这些信息对企业具有商业价值，并且未公开给公众或竞争对手，这类数据在分类分级实施标准中属于',
            correctAnswer: '商业秘密'
        },
        {
            question: '一个现象在某种情况下出现，在另一种情况下没有出现。两种情况除了一个因素全部相同。此种方法属于归纳分析方法中的',
            correctAnswer: '求异法'
        },
        {
            question: '以下关于归纳分析工具的说法正确的是',
            correctAnswer: '归纳分析常见的工具有Matlab、SPSS、SPSSAU、Scikit-Learn等'
        },
        {
            question: '我国职业道德建设规范中是最基本的要求',
            correctAnswer: '爱岗敬业'
        },
        {
            question: '对于重复数据或者错误数据较多，对及时性要求高的项目一般采用检验',
            correctAnswer: '实时'
        },
        {
            question: '是对调查、观察、实验等研究活动中所搜集到的资料进行检验、归类编码和数字编码的过程',
            correctAnswer: '数据整理'
        },
        {
            question: '以下关于数据挖掘与人工智能关系说法错误的是',
            correctAnswer: '机器学习中有些方法借鉴了土木工程学理论，实现数据挖掘目标'
        },
        {
            question: '以下关于数据处理规范说法错误的是',
            correctAnswer: '处理缺失值数据即可'
        },
        {
            question: '对特定字段、记录、文件或数据集中存在重复数据的比例的检查，属于数据质量评价指标中的指标',
            correctAnswer: '准确性'
        },
        {
            question: '数据审核流程中数据审核方式包括',
            correctAnswer: '人工审核、机器检查、指标分析'
        },
        {
            question: '以下关于语音标注质量标准中语音切割转写说法错误的是',
            correctAnswer: '语音内容的转写对于背景音必须进行转写'
        },
        {
            question: '以下不属于人工智能主要学派的是',
            correctAnswer: '机会主义'
        },
        {
            question: '以下关于人工智能与人类智能的关系，说法正确的是',
            correctAnswer: '人工智能是对人类智能的模拟和延伸'
        },
        {
            question: '下列哪项不是人工智能的研究领域',
            correctAnswer: '量子计算'
        },
        {
            question: '以下关于人工智能的应用，错误的是',
            correctAnswer: '人工智能可以完全取代人类教师进行教学'
        },
        {
            question: '人工智能在制造业中的应用，不包括',
            correctAnswer: '员工招聘'
        },
        {
            question: '人工智能中的机器学习算法，不包括',
            correctAnswer: '冒泡排序'
        },
        {
            question: '人工智能的发展历程中，以下哪个阶段是人工智能的起步阶段',
            correctAnswer: '1956 年以前'
        },
        {
            question: '下列关于人工智能发展趋势的说法，错误的是',
            correctAnswer: '人工智能会导致大量人类失业，不需要人类参与任何工作'
        },
        {
            question: '以下哪项不是人工智能在金融领域的应用',
            correctAnswer: '建筑设计'
        },
        {
            question: '深度学习是机器学习的一个分支，它主要基于',
            correctAnswer: '神经网络'
        },
        {
            question: '以下不属于人工智能面临的挑战的是',
            correctAnswer: '人工智能完全取代人类工作问题'
        },
        {
            question: '人工智能在农业领域的应用，不包括',
            correctAnswer: '农产品市场推广'
        },
        {
            question: '以下不属于人工智能核心技术的是',
            correctAnswer: '虚拟现实技术'
        },
        {
            question: '人工智能在教育领域的应用，不包括',
            correctAnswer: '校园安全管理'
        },
        {
            question: '以下关于人工智能对社会的影响，说法正确的是',
            correctAnswer: '既有积极影响也有消极影响'
        },
        {
            question: '人工智能在娱乐领域的应用，不包括',
            correctAnswer: '演员表演'
        },
        {
            question: '人工智能在物流领域的应用，不包括',
            correctAnswer: '快递包裹包装'
        },
        {
            question: '以下哪项不是人工智能在智能家居中的应用',
            correctAnswer: '家庭装修设计'
        },
        {
            question: '以下关于人工智能算法的特点，说法错误的是',
            correctAnswer: '可以解决所有问题'
        },
        {
            question: '数据标注的质量直接影响的性能',
            correctAnswer: '人工智能模型'
        },
        {
            question: '数据标注在自动驾驶领域的应用，不包括',
            correctAnswer: '驾驶员情绪标注'
        },
        {
            question: '以下关于数据标注项目管理的说法，错误的是',
            correctAnswer: '不需要关注标注质量'
        },
        {
            question: '数据标注在电商领域的应用，不包括',
            correctAnswer: '商品生产工艺标注'
        },
        {
            question: '在图像标注中，对于目标对象的标注，以下哪个属性是不需要标注的',
            correctAnswer: '重量'
        },
        {
            question: '以下不属于数据标注质量控制方法的是',
            correctAnswer: '增加标注员数量'
        },
        {
            question: '数据标注的一致性是指',
            correctAnswer: '不同标注员对同一数据的标注结果相同、同一标注员在不同时间对同一数据的标注结果相同、标注结果符合数据的实际情况'
        },
        {
            question: '以下关于数据标注的未来发展趋势，说法错误的是',
            correctAnswer: '对标注员的要求会降低'
        },
        {
            question: '在数据标注过程中，如果遇到不确定的标注情况，应该',
            correctAnswer: '咨询相关人员或参考标注规范'
        },
        {
            question: '数据处理的目的不包括',
            correctAnswer: '增加数据存储量'
        },
        {
            question: '数据标注在医疗领域的应用，不包括',
            correctAnswer: '医生诊断标注'
        },

        // 判断题
        {
            question: '数据集中的训练集、验证集和测试集，这三个集合可以有交集',
            correctAnswer: '错误'
        },
        {
            question: 'shell 脚本是一门解释性的语言，代码不需要进行编译',
            correctAnswer: '正确'
        },
        {
            question: '为了获取丰富的数据样本，对于采集渠道优化可以采用机器进行多元场景自动化采集',
            correctAnswer: '正确'
        },
        {
            question: '数据质量是企业应用数据的瓶颈，高质量的数据可以决定数据应用的下限',
            correctAnswer: '错误'
        },
        {
            question: '《中华人民共和国网络安全法》规定，网络运营者不得泄露、篡改、毁损其收集的个人信息；未经被收集者同意，不得向他人提供个人信息',
            correctAnswer: '正确'
        },
        {
            question: '通过请求转发来实现目标资源的访问是服务器内部的行为，对于客户端来说是一次请求过程',
            correctAnswer: '正确'
        },
        {
            question: '在及时性监测中发现质量问题可以不用告警',
            correctAnswer: '错误'
        },
        {
            question: 'SPSS 中数据编辑窗口中的一行称为一个变量',
            correctAnswer: '错误'
        },
        {
            question: '职业道德有利于提高员工职业技能，增强企业竞争力',
            correctAnswer: '正确'
        },
        {
            question: '数据采集规范要求承担信息收集、利用、公布职能的机构要采取充分的管理措施和技术手段，来保证个人数据的保密性、安全性',
            correctAnswer: '正确'
        },
        {
            question: '机器学习是实现人工智能的途径之一，而深度学习则是机器学习的算法之一',
            correctAnswer: '正确'
        },
        {
            question: '数据分析的方法是理论，而数据分析的工具就是我们实现理论的利器',
            correctAnswer: '正确'
        },
        {
            question: '人工智能的概念于1956年召开的达特茅斯会议上被提出',
            correctAnswer: '正确'
        },
        {
            question: '目前比较常见的智能方法包括模糊计算、粗糙集与粒计算、群智能、神经网络、进化计算、人工免疫系统等',
            correctAnswer: '正确'
        },
        {
            question: '信息是数据的表达，数据是信息的内涵',
            correctAnswer: '错误'
        },
        {
            question: '项目验收要求与客户在前期对项目提出的《招标文件》以及客户与团队的验收标准相一致',
            correctAnswer: '正确'
        },
        {
            question: '描述性统计，是指运用制表和分类，图形以及计算概括性数据来描述数据特征的各项活动',
            correctAnswer: '正确'
        },
        {
            question: '数据分析的重点是从数据中发现知识规则而数据挖掘的重点是观察数据',
            correctAnswer: '错误'
        },
        {
            question: '层次聚类算法是典型的分类算法',
            correctAnswer: '错误'
        },
        {
            question: '业务指标是指某业务的数据记录，经过统计与分析后被用作了解业务的相关数据指标',
            correctAnswer: '正确'
        },
        {
            question: '错误标注的数据不会影响模型的准确率，所以为了节省成本，不需要审核员来对标注数据进行质量检验',
            correctAnswer: '错误'
        },
        {
            question: 'ETL 中的数据装载，通常采用直接 SQL 语句和批量装载两种方式',
            correctAnswer: '正确'
        },
        {
            question: '数据集中的数据与数据源中的数据总是完全相同的',
            correctAnswer: '错误'
        },
        {
            question: '高速中车牌号码的识别主要采用的数据分析技术',
            correctAnswer: '错误'
        },
        {
            question: '数据标注仅仅就是随着人工智能崛起而产生的一种新兴职业',
            correctAnswer: '错误'
        },
        {
            question: '图表相关分析、协方差及协方差矩阵、相关系数、一元回归及多元回归和信息熵及互信息是相关分析方法中最常用的五种',
            correctAnswer: '正确'
        },
        {
            question: '语音标注时，标注与发音时间轴误差应在 1 个语音帧以内',
            correctAnswer: '正确'
        },
        {
            question: 'LabelImg 标框标注工具是有 exe 程序按照版本的',
            correctAnswer: '正确'
        },
        {
            question: '数据处理流程优化中可以通过数据资产信息系统化的方式建设数据处理自动化过程，提升处理效率',
            correctAnswer: '正确'
        },
        {
            question: '数据分类是数据资产管理的第一步，其作用是只能对数据进行分级',
            correctAnswer: '错误'
        },
        {
            question: 'SPSS 只能下载安装包安装，不能采用下载解压缩方式安装',
            correctAnswer: '错误'
        },
        {
            question: '网络爬虫可以爬取互联网上任意的网页',
            correctAnswer: '错误'
        },
        {
            question: '数据分类分级制度完美无瑕，不存在任何缺陷与挑战',
            correctAnswer: '错误'
        },
        {
            question: '语音标注验收报告包括多段落语音验收报告',
            correctAnswer: '正确'
        },
        {
            question: '在常用的销售人员绩效指标中，访问成功率是衡量业务人员工作效率的指标',
            correctAnswer: '正确'
        },
        {
            question: '数据整理中归纳法的优点是结论可靠',
            correctAnswer: '错误'
        },
        {
            question: '腾讯在线协作文档可以用于知识整理',
            correctAnswer: '正确'
        },
        {
            question: '职业道德是活动中的行为规范，不属于社会道德的总范畴',
            correctAnswer: '错误'
        },
        {
            question: '数据挖掘是指从大量的数据中通过算法搜索隐藏于其中信息的过程',
            correctAnswer: '正确'
        },
        {
            question: '数据语义学是研究计算机编程和其他使用数据的领域中特定数据的意义和使用的',
            correctAnswer: '正确'
        }
    ];

    // 简答题
    const shortAnswerQuestions = [
        {
            question: '当今科技发展浪潮中，人工智能技术在不同行业和生活场景里发挥着重要作用，请列举五个典型的应用领域，并分别给出具体的应用实例。相较于传统技术，人工智能在实际应用中展现出独特的优势与特性。请概括人工智能应用所具备的五个显著特点，并对每个特点进行简要说明。',
            answer: '人工智能的应用：1.自然语言处理：语音助手、翻译、聊天机器人。2.计算机视觉：图像识别、自动驾驶、医疗影像分析。3.机器学习：推荐系统、预测分析、欺诈检测。4.自动驾驶与机器人：无人车、无人机、自动化机器人。5.医疗：疾病诊断、个性化医疗、健康监测。6.金融：智能投资、风险管理、自动交易。7.娱乐：游戏AI、内容生成、虚拟现实。8.智能家居：智能设备控制、家居自动化。人工智能的应用特点:1.自学习能力：通过数据训练，不断优化表现。2.自动化：减少人工干预，自动执行复杂任务。3.处理海量数据：高效分析和处理大规模数据集。4.模式识别：识别图像、声音、文本等复杂模式。5.智能决策：根据数据和环境做出最佳选择。6.适应性：根据经验和新信息进行调整和改进。'
        },
        {
            question: '当图像数据出现部分遮挡、画面模糊的情况时，怎样合理开展标注工作以确保标注结果的准确性？',
            answer: '对于遮挡问题，若遮挡部分不影响主要目标识别，可根据可见部分和上下文信息进行标注；若影响较大，可标记为遮挡状态或通过多视角图像辅助标注。对于模糊问题，尽量利用图像中的清晰部分和先验知识进行标注，若无法准确标注，可标记为模糊待处理，后续根据更多信息或专业人员判断。'
        },
        {
            question: '为了让机器更精准地 "听懂" 人类语言，在语音识别技术的实际应用与优化过程中，可通过哪些途径来提升其识别的精确程度？',
            answer: '可以采取以下方法：增加训练数据量，以涵盖更多的语音变化；使用更先进的声学模型和语言模型；进行数据增强，如添加噪声、语速变化等；优化模型的超参数；使用多模态信息（如结合唇形等视觉信息）；对语音数据进行预处理，如去除噪声等。'
        },
        {
            question: '在数据预处理环节，将数据转化为零均值、单位方差分布的标准化操作，与把数据映射至固定区间（如 [0,1]）的归一化处理，二者存在哪些本质差异？',
            answer: '数据标准化是将数据转换为具有零均值和单位方差的分布，使得不同特征在数值上具有可比性；而归一化是将数据映射到特定的范围（如 [0,1]）内。标准化更关注数据的分布特性，而归一化主要是为了消除不同特征量纲的影响，方便后续的计算和处理。'
        },
        {
            question: '从计算机视觉和人工智能角度出发，能够实现车辆关键信息自动提取，并广泛应用于停车管理等场景的技术，如何定义？其涵盖哪些常见功能？当利用技术手段对车辆进行识别时，从原始图像到形成有效识别数据，需要经过怎样的处理过程？请按步骤进行说明',
            answer: '车辆识别是一种基于计算机视觉和人工智能的技术，用于自动检测和识别图像或视频中的车辆类型、车牌号码、品牌型号等信息。它通常应用在智能交通系统、停车管理、安防监控等领域。车辆识别的常见功能包括：1.车牌识别（LPR/ANPR）：自动识别和读取车牌号。2.车辆分类：区分车辆类型，如轿车、卡车、公交车等。3.车辆品牌和型号识别：识别车辆的品牌、型号、颜色等外观特征。处理过程：1.图像采集 2.图像预处理 3.车辆检测 4.特征提取 5.分类与识别 6.结果输出 7.数据存储与处理'
        },
        {
            question: '语料标准化是将客户的原始语料相似问题转化成标准问题的一个过程，为了使在线机器人更高效、准确的识别问题，需要将已经清洗处理的客户原始语料转化为标准语料。假如你是某电商平台智能客服系统的语料整理员，需要在以下客户原始语料中找出与标准语料"如何申请退款？"对应的语料（退款指将已支付款项退回原支付账户）',
            answer: '1、3、4、5、6、8、9'
        },
        {
            question: '命名实体识别在自然语言处理中的主要目标是什么？它对其他NLP任务有何重要性？',
            answer: '任务是识别文本中的具有特定意义的实体，如人名、地名、组织机构名等。意义在于为进一步的文本理解和处理提供基础，有助于信息抽取、问答系统、机器翻译等任务的实现。例如，在信息抽取中可以快速提取关键实体信息，提高处理效率。'
        },
        {
            question: '在将训练好的模型投入实际应用时，需要评估哪些技术要素以确保系统高效稳定运行？',
            answer: '要考虑目标平台的性能和资源限制，如计算能力、内存等；模型的压缩和优化，以减少模型大小和计算量；接口设计，使其能与其他系统进行交互；实时性要求，确保模型能够及时响应；安全性和稳定性，保证模型的可靠运行；还需考虑部署的成本和维护的便捷性等。'
        },
        {
            question: '在人工智能技术范畴内，有一种技术能够让计算机像人类一样 "认识" 图像，它借助计算机视觉算法，对图像中的物体、场景等进行自动分辨归类，还能运用机器学习和深度学习模型，从图像和视频里提取并分析各类信息，在身份验证、交通管理、文档处理等方面发挥着重要作用。请问这项技术是什么？请阐述其定义。在深度学习兴起之前，科研人员依靠一系列既定流程实现图像识别任务，这些流程环环相扣，共同完成对图像的识别与分类工作。请系统地梳理传统图像识别的具体流程',
            answer: '图像识别是人工智能领域中的一项技术，指的是通过计算机视觉算法，自动识别和分类图像中的物体、场景或特定特征。它利用机器学习和深度学习模型，能够从图片或视频中提取信息并进行分析，常见的应用包括：面部识别：识别和验证人的面部特征。物体检测：识别图像中的不同物体，如车辆、动物等。文字识别（OCR）：从图像中提取和识别文字。传统图像识别流程：1.图像预处理 2.特征提取 3.特征选择 4.分类与匹配 5.结果输出'
        },
        {
            question: '人工智能的定义及分类。人工智能与计算机程序相比对，优点有那些？',
            answer: '人工智能（AI） 是一种使机器具备模拟或执行人类智能行为的技术，包含感知、推理、学习、规划和决策等能力。AI可以通过数据分析、模式识别和自我改进来完成复杂任务，通常分为三类：弱人工智能、强人工智能和超人工智能。人工智能与计算机程序相比对，优点有：（1）具备自主学习和适应能力：1.传统计算机程序：根据预定规则和算法执行特定任务。其行为是确定性的，依赖于编写者的指令。2.人工智能：具备自主学习和适应能力，能够从数据中学习，并根据环境或经验调整行为。（2）灵活性更高：1.计算机程序：严格遵循编写时定义的规则，无法自主改进或扩展。2.人工智能：具备自我学习和调整的能力，通过训练算法提高表现，例如机器学习中的模型可以通过数据进行训练并优化。（3）处理复杂性能力更强：1.传统程序：适合处理简单、明确的任务，无法应对不确定性和模糊的情况。2.人工智能：擅长处理复杂的、模糊或不确定的情况，可以进行模式识别和预测。'
        },
        {
            question: '有一种模型通过让两个组件展开 "猫鼠游戏" 来提升数据生成质量，解释该模型的工作原理以及这两个组件的具体职责。',
            answer: '基本原理是通过生成器和判别器的对抗训练来生成逼真的数据。生成器试图生成假数据来欺骗判别器，而判别器则要区分真实数据和生成器生成的数据。主要组成部分为生成器和判别器。生成器学习生成逼真的数据，判别器则不断提升区分能力，两者相互竞争，促使生成器生成更接近真实数据的样本。'
        }
    ];

    // 创建简化版悬浮窗
    function createFloatingWindow() {
        const floatDiv = document.createElement('div');
        floatDiv.id = 'autoAnswerFloat';
        floatDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.95);
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
            z-index: 9999;
            width: 280px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            font-family: Arial, sans-serif;
            transition: opacity 0.3s ease, box-shadow 0.3s ease;
            opacity: 0.0; /* 初始设置为低透明度 */
        `;

        // 创建标题栏（用于拖拽）
        const titleBar = document.createElement('div');
        titleBar.style.cssText = `
            padding: 5px;
            margin: -10px -10px 10px -10px;
            background: #f0f0f0;
            border-bottom: 1px solid #ccc;
            border-radius: 5px 5px 0 0;
            cursor: move;
            display: flex;
            justify-content: space-between;
            align-items: center;
        `;

        const title = document.createElement('div');
        title.textContent = '自动答题助手';
        title.style.fontWeight = 'bold';

        const closeButton = document.createElement('button');
        closeButton.innerHTML = '&times;';
        closeButton.style.cssText = `
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #666;
        `;
        closeButton.onclick = function() {
            floatDiv.style.display = 'none';
            document.getElementById('showFloatButton').style.display = 'block';
        };

        titleBar.appendChild(title);
        titleBar.appendChild(closeButton);
        floatDiv.appendChild(titleBar);

        // 内容容器
        const contentDiv = document.createElement('div');
        contentDiv.id = 'floatContent';

        // 开始/暂停按钮
        const startPauseButton = document.createElement('button');
        startPauseButton.id = 'startPauseButton';
        startPauseButton.textContent = '开始答题';
        startPauseButton.style.cssText = `
            margin: 5px;
            padding: 8px 12px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            width: 100%;
        `;
        startPauseButton.onmouseover = function() { this.style.backgroundColor = '#45a049'; };
        startPauseButton.onmouseout = function() { this.style.backgroundColor = '#4CAF50'; };
        startPauseButton.onclick = toggleAnswering;

        // 显示窗口按钮（当窗口被隐藏时使用）
        const showButton = document.createElement('button');
        showButton.id = 'showFloatButton';
        showButton.textContent = '显示答题助手';
        showButton.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            padding: 10px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            z-index: 9998;
            display: none;
        `;
        showButton.onclick = function() {
            floatDiv.style.display = 'block';
            this.style.display = 'none';
        };
        document.body.appendChild(showButton);

        // 当前题目信息区域
        const currentQuestionInfo = document.createElement('div');
        currentQuestionInfo.id = 'currentQuestionInfo';
        currentQuestionInfo.style.cssText = `
            margin-top: 10px;
            padding: 10px;
            background: #f0f8ff;
            border-radius: 4px;
            border-left: 4px solid #2196F3;
        `;
        currentQuestionInfo.innerHTML = '等待开始...';

        contentDiv.appendChild(startPauseButton);
        contentDiv.appendChild(currentQuestionInfo);

        floatDiv.appendChild(contentDiv);
        document.body.appendChild(floatDiv);

        // 添加鼠标悬停和离开事件
        floatDiv.addEventListener('mouseenter', function() {
            floatDiv.style.opacity = '1'; // 鼠标悬停时完全显示
        });

        floatDiv.addEventListener('mouseleave', function() {
            floatDiv.style.opacity = '0.0'; // 鼠标离开时降低透明度
        });

        // 实现拖拽功能
        let isDragging = false;
        let offsetX, offsetY;

        titleBar.addEventListener('mousedown', function(e) {
            isDragging = true;
            offsetX = e.clientX - floatDiv.getBoundingClientRect().left;
            offsetY = e.clientY - floatDiv.getBoundingClientRect().top;
            floatDiv.style.boxShadow = '0 8px 16px rgba(0,0,0,0.3)';
        });

        document.addEventListener('mousemove', function(e) {
            if (!isDragging) return;

            const x = e.clientX - offsetX;
            const y = e.clientY - offsetY;

            // 确保窗口不会被拖出视口
            const maxX = window.innerWidth - floatDiv.offsetWidth;
            const maxY = window.innerHeight - floatDiv.offsetHeight;

            floatDiv.style.left = Math.max(0, Math.min(x, maxX)) + 'px';
            floatDiv.style.top = Math.max(0, Math.min(y, maxY)) + 'px';
            // 修复拖拽问题：移除right属性，确保位置由left和top控制
            floatDiv.style.right = '';
        });

        document.addEventListener('mouseup', function() {
            isDragging = false;
            floatDiv.style.boxShadow = '0 4px 8px rgba(0,0,0,0.2)';
        });
    }

    // 辅助函数：生成随机延迟时间
    function getRandomDelay(min, max) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }

    // 添加鼠标事件处理
function initMouseEvents() {
  document.addEventListener('mousemove', handleMouseMove);
  document.addEventListener('click', handleMouseClick);
}

function handleMouseMove() {
  resetIdleTimer();

  if(autoMode) {
    autoMode = false;
    console.log('切换到手动模式');
    document.getElementById('startPauseButton').textContent = '暂停答题(手动模式)';
    document.getElementById('startPauseButton').style.backgroundColor = '#ff9800';
  }
}

function handleMouseClick(e) {
  // 检查是否点击了正确答案
  const correctOption = document.querySelector('[data-correct="true"]');
  if(correctOption && correctOption.contains(e.target)) {
    // 显示忙状态
    document.body.style.cursor = 'wait';
    setTimeout(() => {
      document.body.style.cursor = '';
    }, 1000);
  }
}

function resetIdleTimer() {
  clearTimeout(idleTimer);
  idleTimer = setTimeout(() => {
    if(!autoMode) {
      autoMode = true;
      console.log('10秒无操作，切换回自动模式');
      document.getElementById('startPauseButton').textContent = '暂停答题';
      document.getElementById('startPauseButton').style.backgroundColor = '#f44336';
    }
  }, IDLE_TIMEOUT);
}

// 辅助函数：模拟鼠标移动和点击
function simulateHumanClick(element) {
        return new Promise(resolve => {
            // 先模拟鼠标悬停
            const mouseoverEvent = new MouseEvent('mouseover', {
                bubbles: true,
                cancelable: true,
                view: window
            });
            element.dispatchEvent(mouseoverEvent);

            // 随机延迟后模拟点击
            setTimeout(() => {
                const clickEvent = new MouseEvent('click', {
                    bubbles: true,
                    cancelable: true,
                    view: window
                });
                element.dispatchEvent(clickEvent);
                resolve();
            }, getRandomDelay(300, 1200)); // 随机延迟300-1200ms
        });
    }

    // 全局变量，用于控制答题过程
let isAnswering = false;
let pauseAnswering = false;
let currentQuestionRows = [];
let currentIndex = 0;
// 移除不需要的计数变量
let totalQuestions = 0;

// 添加全局变量
let autoMode = true;
let idleTimer = null;
const IDLE_TIMEOUT = 10000; // 10秒

    // 开始/暂停答题切换函数
    function toggleAnswering() {
        const button = document.getElementById('startPauseButton');

        if (!isAnswering) {
            // 开始答题
            isAnswering = true;
            pauseAnswering = false;
            button.textContent = '暂停答题';
            button.style.backgroundColor = '#f44336';

            // 重置计数器
            currentQuestionRows = document.querySelectorAll('#papertable tr[id^="tr"]');
            totalQuestions = currentQuestionRows.length;
            currentIndex = 0;
            // 移除不需要的计数重置

            // 开始答题过程
            processNextQuestion();
        } else {
            if (pauseAnswering) {
                // 继续答题
                pauseAnswering = false;
                button.textContent = '暂停答题';
                button.style.backgroundColor = '#f44336';
                processNextQuestion();
            } else {
                // 暂停答题
                pauseAnswering = true;
                button.textContent = '继续答题';
                button.style.backgroundColor = '#ff9800';
            }
        }
    }

    // 处理下一个问题
    async function processNextQuestion() {
        if (pauseAnswering || !isAnswering || !autoMode) return;

        if (currentIndex >= totalQuestions) {
            // 答题完成
            finishAnswering();
            return;
        }

        const row = currentQuestionRows[currentIndex];
        currentIndex++;

        // 添加滚动到当前题目的功能
        row.scrollIntoView({ behavior: 'smooth', block: 'center' });

        // 获取题目文本
        const questionDiv = row.querySelector('div[name^="DIV"]');
        if (!questionDiv) {
            processNextQuestion();
            return;
        }

        const questionTable = questionDiv.querySelector('table');
        if (!questionTable) {
            processNextQuestion();
            return;
        }

        const questionTd = questionTable.querySelector('td[valign="top"][style*="width"]');
        if (!questionTd) {
            processNextQuestion();
            return;
        }

        // 提取题目文本
        const paragraphs = questionTd.querySelectorAll('p');
        if (paragraphs.length < 2) {
            processNextQuestion();
            return;
        }

        const questionText = paragraphs[0].textContent.trim();
        // 移除题号和括号
        const cleanQuestion = questionText.replace(/^\d+、\s*|（\s*）\s*。*$/g, '');

        // 随机暂停一段时间，模拟阅读题目
        await new Promise(resolve => setTimeout(resolve, getRandomDelay(1000, 3000)));

        // 提取所有选项文本和对应的按钮
        const optionsMap = new Map();

        // 检查是否是多选题（查找checkbox类型的输入）
        const checkboxes = row.querySelectorAll('input[type="checkbox"]');
        const isMultipleChoice = checkboxes.length > 0;

        // 根据题目类型获取选项
        const options = isMultipleChoice ? checkboxes : row.querySelectorAll('input[type="radio"]');

        // 判断是否是判断题
        const isJudgmentQuestion = options.length === 2 &&
            ((options[0].nextSibling && options[0].nextSibling.textContent.includes('正确')) ||
             (options[1].nextSibling && options[1].nextSibling.textContent.includes('错误')));

        // 检查是否包含"以上都是"选项，如果有则跳过此题
        let hasAllOfTheAbove = false;

        for (let i = 0; i < paragraphs.length; i++) {
            const text = paragraphs[i].textContent.trim();
            if (text.includes('以上都是')) {
                hasAllOfTheAbove = true;
                break;
            }
        }

        if (hasAllOfTheAbove) {
            // 更新当前题目信息
            document.getElementById('currentQuestionInfo').innerHTML =
                `当前题目：${cleanQuestion}<br>答案：[跳过 - 包含"以上都是"选项]`;

            // 随机暂停，模拟思考时间
            await new Promise(resolve => setTimeout(resolve, getRandomDelay(800, 2000)));

            // 处理下一个问题
            processNextQuestion();
            return;
        }

        options.forEach(option => {
            // 获取选项标签（A、B、C、D）
            const optionLabel = option.nextSibling ? option.nextSibling.textContent.trim() : '';

            // 获取选项内容
            const optionIndex = Array.from(options).indexOf(option);
            if (optionIndex >= 0 && optionIndex + 1 < paragraphs.length) {
                const optionText = paragraphs[optionIndex + 1].textContent.trim();
                // 提取选项内容（去除前面的A.、B.等标识）
                const optionContent = optionText.replace(/^\s*[A-D]\.\s*/, '').trim();
                optionsMap.set(optionContent, option);
            }
        });

        let found = false;
        let matchedQuestion = null;
        let correctAnswer = '';

        // 遍历题库中的每个问题
        for (let i = 0; i < questionBank.length && !found; i++) {
            const qbItem = questionBank[i];

            // 改进匹配逻辑：规范化处理题目文本，移除括号和多余空格
            const normalizedQuestion = cleanQuestion.replace(/（\s*）/g, '').replace(/\s+/g, ' ').trim();
            const normalizedBankQuestion = qbItem.question.replace(/（\s*）/g, '').replace(/\s+/g, ' ').trim();

            // 使用更灵活的匹配方式
            if (normalizedQuestion.includes(normalizedBankQuestion) ||
                normalizedBankQuestion.includes(normalizedQuestion) ||
                // 计算相似度，如果相似度超过80%也认为匹配
                calculateSimilarity(normalizedQuestion, normalizedBankQuestion) > 0.8) {

                matchedQuestion = qbItem;
                correctAnswer = qbItem.correctAnswer;

                // 判断题的特殊处理
                if (isJudgmentQuestion) {
                    // 判断题的正确答案通常是"正确"或"错误"
                    const correctOption = correctAnswer.includes('正确') ? options[0] : options[1];

                    // 模拟人类点击行为
                    await simulateHumanClick(correctOption);
                    correctOption.checked = true;
                    found = true;
                }
                // 处理多选题的情况
                else if (isMultipleChoice) {
                    // 多选题的正确答案可能是多个，用分隔符分开
                    const correctAnswers = qbItem.correctAnswer.split('、').map(ans => ans.trim());

                    // 遍历所有选项，选中正确答案
                    for (const [optionContent, option] of optionsMap.entries()) {
                        // 检查当前选项是否是正确答案之一
                        const isCorrect = correctAnswers.some(answer =>
                            optionContent === answer ||
                            optionContent.includes(answer) ||
                            answer.includes(optionContent)
                        );

                        if (isCorrect) {
                            // 模拟人类点击行为
                            await simulateHumanClick(option);
                            option.checked = true;
                            found = true;
                        }
                    }
                } else {
                    // 精确匹配：遍历所有选项，寻找与正确答案完全匹配的选项
                    for (const [optionContent, option] of optionsMap.entries()) {
                        // 使用精确匹配而不是包含匹配
                        if (optionContent === qbItem.correctAnswer) {
                            // 模拟人类点击行为
                            await simulateHumanClick(option);
                            option.checked = true;

                            // 移除计数增加
                            found = true;
                            break;
                        }
                    }

                    // 如果没有找到精确匹配，尝试部分匹配
                    if (!found) {
                        for (const [optionContent, option] of optionsMap.entries()) {
                            if (optionContent.includes(qbItem.correctAnswer) ||
                                qbItem.correctAnswer.includes(optionContent)) {
                                // 模拟人类点击行为
                                await simulateHumanClick(option);
                                option.checked = true;

                                // 移除计数增加
                                found = true;
                                break;
                            }
                        }
                    }
                }
            }
        }

        // 添加一个计算字符串相似度的函数
        function calculateSimilarity(str1, str2) {
            // 简单的相似度计算：共同字符数 / 较长字符串长度
            const set1 = new Set(str1);
            const set2 = new Set(str2);
            const intersection = new Set([...set1].filter(x => set2.has(x)));
            return intersection.size / Math.max(set1.size, set2.size);
        }

        if (!found) {
            // 移除计数增加
            correctAnswer = '未找到匹配答案';
        }

        // 更新当前题目信息
        document.getElementById('currentQuestionInfo').innerHTML =
            `当前题目：${cleanQuestion}<br>答案：${correctAnswer}`;

        // 随机暂停，模拟思考时间
        if (Math.random() < 0.3) { // 30%概率进行较长暂停
            await new Promise(resolve => setTimeout(resolve, getRandomDelay(3000, 8000)));
        } else {
            await new Promise(resolve => setTimeout(resolve, getRandomDelay(800, 2000)));
        }

        // 处理下一个问题
        if (!pauseAnswering) {
            processNextQuestion();
        }
    }

    // 完成答题
    function finishAnswering() {
        isAnswering = false;
        document.getElementById('startPauseButton').textContent = '开始答题';
        document.getElementById('startPauseButton').style.backgroundColor = '#4CAF50';
        document.getElementById('currentQuestionInfo').innerHTML = '答题完成！';
    }

    // 页面加载完成后创建悬浮窗
window.addEventListener('load', () => {
    if (window.location.href.includes('************')) {
        // 随机延迟创建，避免固定时间特征
        setTimeout(createFloatingWindow, getRandomDelay(1500, 3000));
        // 初始化鼠标事件监听
        initMouseEvents();
    }
});
})();