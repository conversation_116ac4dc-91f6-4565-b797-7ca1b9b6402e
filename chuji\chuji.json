[{"question": "以下不属于人工智能训练师在业务知识库维护方面的工作是（ ）。", "answer": "编写人工智能算法的文档"}, {"question": "若要查询名字以 “张” 开头的学生，应该使用（ ）。", "answer": "SELECT * FROM students WHERE name LIKE ' 张 %';"}, {"question": "随着社会的发展，道德和法律的关系是（ ）。", "answer": "越来越融合"}, {"question": "职业道德对于企业的重要性体现在（ ）。", "answer": "有助于增强企业凝聚力，促进企业的可持续发展"}, {"question": "随着人工智能技术的发展，数据标注（ ）。", "answer": "对标注质量和效率的要求越来越高"}, {"question": "社会主义道德建设与市场经济的关系是（ ）。", "answer": "社会主义道德建设为市场经济的健康发展提供精神动力和价值支撑"}, {"question": "完整性规则在关系数据模型中的作用是（ ）。", "answer": "保证数据的准确性、一致性和完整性"}, {"question": "价值分析在人工智能产品构建中主要用于（ ）。", "answer": "评估产品的商业价值和市场潜力"}, {"question": "高质量的数据标注能够（ ）。", "answer": "提高人工智能模型的准确性和泛化能力"}, {"question": "计算机从业者在开源社区分享代码时，应该（ ）。", "answer": "遵守开源协议，尊重他人的知识产权，并提供清晰的文档和说明"}, {"question": "下列属于计算机从业者应遵守的道德准则的是（ ）。", "answer": "积极分享知识，帮助团队成员提升技术能力"}, {"question": "在人工智能产品构建的各个阶段中，（ ）阶段需要与业务人员密切合作。", "answer": "商业模式设计、数据洞察、业务转化"}, {"question": "人工智能训练师负责对训练数据进行（ ）。", "answer": "收集、整理和标注"}, {"question": "数据完整性包括（ ）。", "answer": "实体完整性、参照完整性和用户定义的完整性；；数据的准确性、一致性和可靠性；；保证数据符合特定的规则和约束"}, {"question": "人工智能训练师与算法工程师的合作主要体现在（ ）。", "answer": "训练师提供高质量的训练数据，算法工程师根据数据优化模型；；算法工程师向训练师提供算法原理和参数说明，训练师协助调整参数；；共同评估模型的性能，提出改进方向"}, {"question": "熟练掌握深度学习开源框架如 TensorFlow、PyTorch 等，主要是为了（ ）。", "answer": "更方便地构建、训练和部署深度学习模型"}, {"question": "假定 student 表中有姓名字段 name。现要查询所有姓 “李” 且名字是两个字的学生，正确的 SQL 语句是（ ）。", "answer": "SELECT * FROM student WHERE name LIKE ' 李__';"}, {"question": "Oracle 数据库的特点是（ ）。", "answer": "功能强大，具有高度的可扩展性和可靠性，适用于大型企业和关键业务系统"}, {"question": "云计算为人工智能提供了（ ）。", "answer": "强大的计算资源和存储能力；；丰富的算法库和模型；；高效的数据传输通道"}, {"question": "虚拟助手（如 Siri、小爱同学等）利用了人工智能的（ ）技术。", "answer": "语音识别、自然语言处理、知识图谱"}, {"question": "算法工程师在模型优化过程中，通常会（ ）。", "answer": "调整模型的超参数，尝试不同的算法和架构；；增加训练数据的数量和质量；；对数据进行预处理和特征工程"}, {"question": "明斯基是人工智能领域的重要人物，他的主要贡献是（ ）。", "answer": "发明了 LISP 语言；；提出了框架理论；；开创了计算机视觉研究"}, {"question": "选择 Python 作为人工智能开发的常用语言，主要是因为（ ）。", "answer": "丰富的科学计算和机器学习库，简洁易读的语法"}, {"question": "数据标准化是数据预处理中的一项重要工作，其目的是（ ）。", "answer": "消除数据特征之间的量纲差异，便于后续分析"}, {"question": "医疗影像诊断中应用人工智能技术，主要是利用其（ ）能力。", "answer": "图像分析和识别"}, {"question": "若要查询性别为男且成绩在 70 到 90 之间或者性别为女且成绩在 80 以上的学生，正确的 SQL 语句是（ ）。", "answer": "SELECT * FROM students WHERE (gender = ' 男 ' AND score BETWEEN 70 AND 90) OR (gender = ' 女 ' AND score > 80);"}, {"question": "社会主义道德建设的基本任务是（ ）。", "answer": "培养有理想、有道德、有文化、有纪律的社会主义公民"}, {"question": "若要查询名字中包含 “明” 字的学生，应使用（ ）。", "answer": "SELECT * FROM student WHERE name LIKE '% 明 %';"}, {"question": "数据库系统的数据共享性体现在（ ）。", "answer": "多个用户可以同时访问和使用数据库中的数据；；数据可以在不同的应用程序之间共享；；可以减少数据的冗余存储，提高数据的利用率"}, {"question": "无人驾驶汽车主要依赖于人工智能的（ ）等技术。", "answer": "传感器融合、计算机视觉、决策规划"}, {"question": "在 SELECT 语句中，若要查询成绩大于 80 或者年龄小于 20 的学生，应使用（ ）关键字。", "answer": "OR"}, {"question": "设计并维护智能对话系统的业务知识库架构时，人工智能训练师需要考虑（ ）。", "answer": "知识的分类和层次结构，便于快速检索和匹配；；与其他系统的集成和交互方式；；知识库的可扩展性和维护性"}, {"question": "数据库系统能够解决数据冗余和数据独立性问题，主要是通过（ ）。", "answer": "数据的集中管理和统一控制"}, {"question": "计算机从业者在面对客户需求时，应该（ ）。", "answer": "充分了解客户需求，提供合理的解决方案，并保证服务质量"}, {"question": "ML 是（ ）两个英文单词的缩写。", "answer": "Machine Learning"}, {"question": "关系数据模型中，数据结构主要指的是（ ）。", "answer": "表、记录和字段的组织形式"}, {"question": "在 Excel 中，想要将 A1 单元格中的文本内容全部转换为大写，应使用（ ）函数。", "answer": "UPPER"}, {"question": "在复杂的条件查询中，合理使用括号可以（ ）。", "answer": "明确条件的执行顺序，避免逻辑错误"}, {"question": "下列属于非结构化数据的是（ ）。", "answer": "电子邮件；；社交媒体上的用户评论；；图片"}, {"question": "数据标注对于人工智能模型训练的作用是（ ）。", "answer": "为模型提供准确的输入数据，帮助模型学习到正确的模式"}, {"question": "RNN 主要用于处理（ ）类型的数据。", "answer": "文本序列"}, {"question": "要查询姓 “王” 且名字最后一个字是 “强” 的学生，SQL 语句为（ ）。", "answer": "SELECT * FROM student WHERE name LIKE ' 王 % 强 ';"}, {"question": "数据库系统的三级模式结构（外模式、模式、内模式）有助于提高（ ）。", "answer": "数据的独立性"}, {"question": "冯・诺依曼在计算机领域的重要贡献不包括（ ）。", "answer": "首次提出人工智能概念"}, {"question": "并发控制不属于关系数据模型的组成部分，它主要是为了（ ）。", "answer": "保证多个用户同时访问数据库时数据的一致性和完整性"}, {"question": "要查询既不是数学专业也不是英语专业的学生，正确的 SQL 表达式是（ ）。", "answer": "SELECT * FROM students WHERE NOT (major = ' 数学 ' OR major = ' 英语 ');"}, {"question": "数据操作在关系数据模型中包括（ ）。", "answer": "插入、删除、更新和查询操作"}, {"question": "图灵提出了（ ）的概念，为人工智能的发展奠定了理论基础。", "answer": "图灵机"}, {"question": "良好的职业道德能够帮助员工（ ）。", "answer": "获得职业发展机会，提升个人综合素质"}, {"question": "NLP 是指（ ）。", "answer": "Natural Language Processing"}, {"question": "Excel 中，用于计算标准偏差的函数是（ ）。", "answer": "STDEV"}, {"question": "关于道德在计算机行业的作用，正确的说法是（ ）。", "answer": "道德能够约束从业者的行为，维护行业的健康发展"}, {"question": "在进行数据挖掘之前，对数据进行抽样的主要原因是（ ）。", "answer": "使数据更具代表性，同时降低计算复杂度"}, {"question": "DL 通常代表（ ）。", "answer": "Deep Learning"}, {"question": "在 SELECT 语句中，AND 关键字的优先级（ ）OR 关键字。", "answer": "高于"}, {"question": "如果要在 Excel 中根据多个条件进行求和，应使用（ ）函数。", "answer": "SUMIFS"}, {"question": "当发现同事的代码存在安全隐患时，正确的做法是（ ）。", "answer": "及时向同事指出，并协助其解决问题"}, {"question": "人脸识别技术主要运用了人工智能的（ ）技术。", "answer": "图像识别"}, {"question": "以下关于人工智能算法工程师编程能力的要求，正确的是（ ）。", "answer": "除 Python 或 R 外，了解 C++ 等语言有助于提高模型的运行效率"}, {"question": "人工智能产品构建中，行业洞察阶段的主要任务是（ ）。", "answer": "深入了解目标行业的需求、痛点和竞争态势"}, {"question": "IaaS（基础设施即服务）与人工智能结合，可以（ ）。", "answer": "让用户更方便地部署和管理人工智能计算资源"}, {"question": "当道德和法律发生冲突时，正确的做法是（ ）。", "answer": "综合考虑各种因素，寻求合理的解决方案"}, {"question": "在关系数据模型中，主键的作用是（ ）。", "answer": "唯一标识表中的每一行记录；；建立表与表之间的关联关系；；保证数据的完整性和一致性"}, {"question": "以下不属于数据库系统数据安全性措施的是（ ）。", "answer": "数据压缩"}, {"question": "PaaS（平台即服务）在人工智能开发中的作用是（ ）。", "answer": "提供开发环境和工具，帮助开发者更高效地构建人工智能应用"}, {"question": "以下关于数据标注员的说法，错误的是（ ）。", "answer": "只负责简单的标记工作，不需要理解数据的含义"}, {"question": "数据挖掘前，对缺失值进行处理属于（ ）。", "answer": "数据预处理"}, {"question": "选择数据库管理系统时，需要考虑的因素包括（ ）。", "answer": "数据规模和性能要求；；数据安全性和可靠性；；成本和易用性"}, {"question": "关于人工智能与云计算结合的优势，以下说法错误的是（ ）。", "answer": "增加了数据泄露的风险，安全性降低"}, {"question": "当需要对查询结果进行去重并且排序时，可以（ ）。", "answer": "先使用 DISTINCT 关键字去重，再使用 ORDER BY 子句排序"}, {"question": "半结构化数据的特点是（ ）。", "answer": "有一定的结构，但不如结构化数据严格"}, {"question": "人工智能算法工程师需要熟悉多种机器学习算法，以下不属于监督学习算法的是（ ）。", "answer": "K 均值聚类"}, {"question": "CNN 是一种常见的深度学习模型，它的全称是（ ）。", "answer": "Convolutional Neural Network"}, {"question": "以下体现集体主义原则的行为是（ ）。", "answer": "在集体活动中，积极贡献自己的力量，为集体荣誉而努力"}, {"question": "下列不属于职业道德基本要求的是（ ）。", "answer": "追求个人利益最大化"}, {"question": "若要查询不重复的员工姓名和部门组合，SQL 语句应该是（ ）。", "answer": "SELECT DISTINCT name, department FROM employees;"}, {"question": "以下关于 DISTINCT 关键字的说法，错误的是（ ）。", "answer": "会对查询结果进行排序"}, {"question": "以下哪项不是数据预处理的常见操作（ ）。", "answer": "数据可视化"}, {"question": "数据预处理过程中，对异常值的处理方法不包括（ ）。", "answer": "复制异常值以增加数据量"}, {"question": "现要查询所有姓 “孙” 且名字是三个字的学生，并且名字中第二个字是 “中”，SQL 语句应该是（ ）。", "answer": "SELECT * FROM student WHERE name LIKE ' 孙中_';"}, {"question": "产品建模与评估过程中，常用的评估指标不包括（ ）。", "answer": "数据量大小"}, {"question": "我国社会主义道德建设的核心是（ ）。", "answer": "为人民服务"}, {"question": "SaaS（软件即服务）形式的人工智能应用，用户可以（ ）。", "answer": "无需自行搭建和维护基础设施，直接使用人工智能功能"}, {"question": "文件系统在数据管理方面的主要缺点是（ ）。", "answer": "数据冗余度高，数据独立性差"}, {"question": "JAVA 语言在人工智能开发中的优势是（ ）。", "answer": "具有良好的跨平台性和稳定性，适合构建大规模的企业级应用"}, {"question": "在 SELECT 语句中，若要查询年龄大于 30 且小于 40 的员工，正确的写法是（ ）。", "answer": "SELECT * FROM employees WHERE age > 30 AND age < 40;"}, {"question": "设计研发方案阶段需要考虑的因素不包括（ ）。", "answer": "数据的来源和质量"}, {"question": "人工智能的发展历程中，达特茅斯会议具有重要意义，这次会议（ ）。", "answer": "正式确立了人工智能这一术语；；聚集了一批早期的人工智能研究者；；开启了人工智能的研究热潮"}, {"question": "处理非结构化数据时，通常需要先进行（ ）操作。", "answer": "特征提取"}, {"question": "不同的数据库管理系统适用于不同的场景，例如 MySQL 常用于（ ）。", "answer": "小型网站和应用的数据库管理"}, {"question": "集体主义强调（ ）。", "answer": "集体利益高于个人利益，当个人利益与集体利益发生冲突时，个人利益要服从集体利益"}, {"question": "数据库系统的可扩展性体现在（ ）。", "answer": "可以方便地增加新的数据表、字段和记录；；能够适应不断增长的数据量和用户需求；；可以通过添加硬件资源或优化软件来提高性能"}, {"question": "数据库系统的数据独立性是指（ ）。", "answer": "数据的逻辑结构与物理存储结构相互独立；；应用程序与数据库的数据结构相互独立；；数据的存储和管理方式不影响应用程序的使用"}, {"question": "人工智能训练师在分析用户问题和答案时，主要目的是（ ）。", "answer": "发现用户需求和痛点，优化人工智能的回答质量"}, {"question": "以下不属于数据库管理系统的是（ ）。", "answer": "Excel"}, {"question": "对于查询姓 “赵” 的学生，以下 SQL 语句中查询结果最准确的是（ ）。", "answer": "SELECT * FROM student WHERE name LIKE ' 赵 %';"}, {"question": "与文件系统相比，数据库系统的优势不包括（ ）。", "answer": "更简单的操作和维护"}, {"question": "智能推荐系统通常基于（ ）技术实现。", "answer": "机器学习"}, {"question": "在 Excel 中，要统计某列中大于 100 的数值个数，应使用（ ）函数。", "answer": "COUNTIF"}, {"question": "除麦卡锡外，还有（ ）等科学家也在早期对人工智能的发展做出了重要贡献。", "answer": "香农；；维纳；；纽厄尔和西蒙"}, {"question": "Excel 中，VLOOKUP 函数的作用是（ ）。", "answer": "在表格的首列查找指定的值，并返回同一行中指定列处的值"}, {"question": "企业通过加强职业道德建设，可以（ ）。", "answer": "提高产品质量和服务水平，增强企业的市场竞争力"}, {"question": "人工智能算法工程师需要具备的数学基础不包括（ ）。", "answer": "流体力学"}, {"question": "在使用 DISTINCT 关键字时，如果查询的字段中有 NULL 值，（ ）。", "answer": "NULL 值会被当作相同的值处理，只保留一个"}, {"question": "在 SELECT 语句中，若要查询所有不重复的部门名称，应使用（ ）关键字。", "answer": "DISTINCT"}, {"question": "下列关于道德与法律的联系，说法错误的是（ ）。", "answer": "道德和法律都是社会规范，都具有规范性和强制性"}, {"question": "R 语言在人工智能开发中主要用于（ ）。", "answer": "数据处理和统计分析"}, {"question": "数据库管理系统的主要功能不包括（ ）。", "answer": "数据可视化，直接生成各种图表"}, {"question": "C/C++ 语言在人工智能开发中常用于（ ）。", "answer": "对性能要求较高的底层算法实现和优化"}, {"question": "职业道德的养成主要靠（ ）。", "answer": "个人的自觉修养和实践"}, {"question": "数据标注类型除了图像、文本、语音、视频、3D 点云外，还包括（ ）。", "answer": "时间序列数据标注；；地理空间数据标注；；表格数据标注"}, {"question": "对于初学者来说，（ ）语言更容易入门，适合快速上手人工智能开发。", "answer": "Python"}, {"question": "在实际应用中，当需要处理大量结构化数据并保证数据的一致性和完整性时，通常会选择（ ）。", "answer": "数据库系统"}, {"question": "以下关于 WHERE 和 HAVING 关键字的区别，说法正确的是（ ）。", "answer": "WHERE 用于对行数据进行筛选，HAVING 用于对分组后的数据进行筛选"}, {"question": "以下属于结构化数据的是（ ）。", "answer": "关系型数据库中的表格数据"}, {"question": "在 SELECT 语句中，HAVING 关键字通常与（ ）一起使用，用于对分组后的结果进行筛选。", "answer": "GROUP BY"}, {"question": "关于道德与法律的区别，正确的是（ ）。", "answer": "道德调整的范围比法律更广"}, {"question": "在人工智能应用中，不同类型的数据（ ）。", "answer": "可以混合使用，以提高模型的性能"}, {"question": "在一个法治社会中，道德的作用是（ ）。", "answer": "辅助法律的实施，弥补法律的不足"}, {"question": "在 SELECT 语句中，要查询既在部门 A 又在项目 B 中的员工，正确的条件表达式是（ ）。", "answer": "SELECT * FROM employees WHERE department = 'A' AND project = 'B';"}, {"question": "在职业场景中，道德规范只对员工个人有约束，对企业发展没有影响", "answer": "错误"}, {"question": "超市收银台扫描商品条码获取价格信息主要运用的是数据分析技术", "answer": "错误"}, {"question": "通过数据分析可以准确预测股票市场的走势，投资者可以依靠数据分析稳赚不赔", "answer": "错误"}, {"question": "道德是固定不变的，适用于所有时代和所有人", "answer": "错误"}, {"question": "目前人工智能在图像识别领域已经能够达到和人类一样准确的程度，不会出现误判", "answer": "错误"}, {"question": "数据预处理只是简单地对数据进行整理，对后续的数据分析和建模影响不大", "answer": "错误"}, {"question": "数据库系统的安全性比文件系统更高，能够更好地保护数据不被非法访问", "answer": "正确"}, {"question": "数据清洗过程中，对于异常值的处理方法需要根据具体情况选择，不能一概而论", "answer": "正确"}, {"question": "人工智能的发展离不开算法、数据和算力这三个关键要素", "answer": "正确"}, {"question": "在关系型数据库中，除了 SELECT 语句，还可以使用其他语句来查询表中的记录", "answer": "正确"}, {"question": "数据标注是一个简单机械的过程，不需要标注人员具备任何专业知识", "answer": "错误"}, {"question": "随机变量分为离散型随机变量和连续型随机变量，伯努利随机变量是离散型随机变量的一种", "answer": "正确"}, {"question": "数据标注只需要对图像数据进行处理，语音和文本数据不需要标注", "answer": "错误"}, {"question": "处理重复值时，只能选择直接删除重复的数据，没有其他处理方式", "answer": "错误"}, {"question": "CONCAT 函数只能用于连接两个字符串，不能连接多个字符串", "answer": "错误"}, {"question": "ETL 中的数据转换阶段是整个过程中最耗时的环节", "answer": "错误"}, {"question": "高质量的数据标注对提高人工智能模型的性能起着关键作用", "answer": "正确"}, {"question": "DATE_FORMAT 函数可以将日期按照指定的格式进行输出", "answer": "正确"}, {"question": "在抽样调查中，样本容量越大，调查结果就一定越准确", "answer": "错误"}, {"question": "文件系统和数据库系统一样，都能很好地解决数据冗余和数据独立性问题", "answer": "错误"}, {"question": "在 Excel 里，无法设置让单元格只能输入特定格式的数据", "answer": "错误"}, {"question": "人工智能的主要目标仅仅是模拟人类的思维方式，而不是超越人类智能", "answer": "错误"}, {"question": "AVG (column_name) 函数在计算平均值时会将 NULL 值也计算在内", "answer": "错误"}, {"question": "Excel 中的 SUMIFS 函数可以根据多个条件对数据进行求和计算", "answer": "正确"}, {"question": "方差分析的主要目的是检验多个总体的均值是否存在显著差异，而不是判断各总体是否存在方差", "answer": "正确"}, {"question": "ETL 不仅可以将数据从异构数据源转化到关系型数据库，还可以转化到其他类型的存储系统", "answer": "正确"}, {"question": "在 Excel 中，若要对某列数据进行降序排序，只能通过 “数据” 菜单中的排序功能实现", "answer": "错误"}, {"question": "ETL 过程中的数据加载阶段不需要进行任何数据验证", "answer": "错误"}, {"question": "遵守道德准则可能会在短期内让企业利益受损，但从长期看有利于企业的可持续发展", "answer": "正确"}, {"question": "大数据分析能够帮助企业更好地了解客户需求，从而制定更精准的营销策略", "answer": "正确"}]